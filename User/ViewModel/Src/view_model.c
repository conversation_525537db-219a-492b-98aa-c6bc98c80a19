//
// Created by <PERSON><PERSON> on 2024/10/25.
//

#include "view_model.h"
#include "user.h"
#include "comp_setting_item.h"
#include "comp_status_bar.h"
#include "comp_sleep.h"

#if IS_REAL_DEVICE

#include "rf_module.h"
#include "amoled_qspi.h"
#include "rf_tx.h"
#include "nikon_protocol.h"
#include "encoder.h"
#include "data_storage.h"
#include "utils.h"

#endif

void defocused_handle(lv_group_t *group) {
    if (group == NULL) {
        group = lv_group_get_default();
        if (group == NULL) {
            return;
        }
    }
    lv_group_set_editing(group, false);
    lv_obj_t *focus_obj = lv_group_get_focused(group);
    if (focus_obj != NULL) {
        lv_obj_clear_state(focus_obj, LV_STATE_FOCUSED);
        lv_obj_clear_state(focus_obj, LV_STATE_FOCUS_KEY);
    }
#if IS_REAL_DEVICE
    Encoder.defocused_flag = true;
#endif
}

void reset_key_long_press_state() {
    Pages.key_long_press = 0;
    Pages.is_long_pressing = false;
}

void sync_param_battery() {
    if (lv_obj_is_valid(status_bar)) {
        lv_event_send(StatusBar.icon_battery, LV_EVENT_VALUE_CHANGED, NULL);
    }
}

void send_sync_status() {
#if IS_REAL_DEVICE
    if (Setting.last_sync_value != Param.status_bar_sync) {
        update_status_bar();
        Setting.last_camera_link_value = Nikon.has_link_flag;
        Setting.last_sync_value = Param.status_bar_sync;
    }
#endif
}

void send_camera_link_status() {
#if IS_REAL_DEVICE
    if (Setting.last_camera_link_value != Nikon.has_link_flag) {
        update_status_bar();
        Setting.last_camera_link_value = Nikon.has_link_flag;
        Setting.last_sync_value = Param.status_bar_sync;
    }
#endif
}

// 发送扫描完成状态
void send_scan_ch_finished() {
#if IS_REAL_DEVICE
    memset(Setting.scan_ch_arr, 0, sizeof(Setting.scan_ch_arr));
    memcpy(Setting.scan_ch_arr, RF.scan_ch, sizeof(Setting.scan_ch_arr));
    SettingObjs.rotate_img_anim.ready_cb(&SettingObjs.rotate_img_anim);
#endif
}

// 更新Zoom UI
void send_zoom_data() {
#if IS_REAL_DEVICE
    if (PageManager.main_page == &Pages.page[Page_Group_Info]) {
        for (int i = 0; i < GroupItemCount; ++i) {
            if (lv_obj_is_valid(Param.group[i].slider_option_zoom_level.slider_bg) &&
                Param.group[i].slider_option_zoom_level.slider_bg != NULL) {
                lv_event_send(Param.group[i].slider_option_zoom_level.slider_bg, LV_EVENT_VALUE_CHANGED, NULL);
            }
        }
    } else {
        // rf_send_zoom_0x5A();
        Param.RF_send_ref |= RF_zoom_0x5A;
    }
#endif
}

// 模拟长按
void send_lock_maks_long_pressed_event() {
    if (lv_obj_is_valid(Pages.lock_mask) && Setting.lock == 1) {
        uint8_t can_unlock = 1;
        lv_event_send(Pages.lock_mask, LV_EVENT_LONG_PRESSED_REPEAT, (void *) (uintptr_t) can_unlock);
    }
}

// 模拟松手
void send_lock_maks_released_event() {
    if (lv_obj_is_valid(Pages.lock_mask)) {
        lv_event_send(Pages.lock_mask, LV_EVENT_RELEASED, NULL);
    }
}

// 设置开始扫描频道
void set_start_scan_ch() {
#if IS_REAL_DEVICE
    RF.access_scan_ch = true;
#endif
}

// 设置屏幕亮度
void set_screen_brightness() {
#if IS_REAL_DEVICE
    amoled_set_brightness_level(Param.brightness.level);
#endif
}

// 设置蜂鸣器造型灯
void set_beep_lamp_level() {
#if IS_REAL_DEVICE
    Param.RF_send_ref |= RF_lamp_beep_0x8A;
#endif
}

// 设置焦距
void set_zoom_level(uint8_t group_name) {
#if IS_REAL_DEVICE
    if (group_name == GroupItemCount) {
        for (int i = 0; i < GroupItemCount; ++i) {
            if (Param.group[i].is_auto_zoom) {
                Param.group[i].auto_zoom_level = Nikon.last_zoom;
            }
        }
    } else {
        if (Param.group[group_name].is_auto_zoom) {
            Param.group[group_name].auto_zoom_level = Nikon.last_zoom;
        }
    }
    Param.RF_send_ref |= RF_zoom_0x5A;
#endif
}

// 设置模式、能级
void set_flash_mode_level() {
#if IS_REAL_DEVICE
    Param.RF_send_ref |= RF_mode_level_0x8B;
#endif
}

// 设置最大能级
uint8_t set_flash_level_max() {
#if IS_REAL_DEVICE
    return get_m_level_max();
#endif
}

// 设置频闪页参数
void set_multi_param() {
#if IS_REAL_DEVICE
    Param.RF_send_ref |= RF_Multi_param_0x8D;
#endif
}

// 设置重置
void set_reset() {
#if IS_REAL_DEVICE
    fill_default_data();
#endif
}

// 设置刷新RF状态
void set_RF_status() {
#if IS_REAL_DEVICE
    RF.reset_time = 0;
#endif
}

// 获取相机连接标志
bool get_camera_link() {
#if IS_REAL_DEVICE
    return Nikon.has_link_flag;
#endif
}

// 关机
void set_device_off() {
#if IS_REAL_DEVICE
    device_deinit();
#endif
}

// 重启
void set_device_restart() {
#if IS_REAL_DEVICE
    data_reset();
    device_sw_reset();
#endif
}

// 获取第一个聚焦的对象
lv_obj_t *get_page_first_focus_obj() {
    if (PageManager.main_page == &Pages.page[Page_Group_Info]) {
        // // 获取当前活动的tile
        // lv_obj_t *current_tile = lv_tileview_get_tile_act(Param.grp_tile_view);
        //
        // // 获取tile的位置
        // lv_coord_t x = lv_obj_get_x(current_tile);
        // lv_coord_t y = lv_obj_get_y(current_tile);
        //
        // printf("当前tile位置：x = %d, y = %d\n", x, y);

        // 或者获取滚动位置
        // lv_coord_t scroll_x = lv_obj_get_scroll_x(Param.grp_tile_view);
        lv_coord_t scroll_y = lv_obj_get_scroll_y(Param.grp_tile_view);
        // printf("当前滚动位置：x = %d, y = %d\n", scroll_x, scroll_y);
        uint8_t current_tile_group_id = scroll_y / Screen_Height;
        LV_LOG("current_tile_group_id: %d\n", current_tile_group_id);
        return Pages.page[Page_Group_Info].group_obj[current_tile_group_id * 5];
    }
    if (Pages.page[Page_Pop].indev_group) {
        return Pages.page[Page_Pop].group_obj[0];
    }
    printf_main_page();
    return PageManager.main_page->group_obj[0];
}

/**
 * @brief 检测lvgl定时器是否在运行
 * @param timer 定时器指针
 * @return true || false
 */
bool check_timer_is_run(lv_timer_t *timer) {
    if (timer != NULL) {
        if (!timer->paused && lv_timer_get_next(timer) != NULL) {
            // 定时器 timer 未被暂停且存在于链表中（即正在运行）
            return true;
        }
    }
    return false;
}

#if ProductModel == QZ_F

void send_ui_refresh() {
    if (Param.ref_param == Ref_None || !Anim.is_finished_anim) {
        return;
    }
    if (Param.ref_param & Ref_Multi_Home) {
        if (PageManager.main_page == &Pages.page[Page_Multi]) {
            if (!Param.ref_to_multi) {
                pm_creat_page(PageManager.main_page->pos[pos_right].page, PageManager.main_page, LV_ALIGN_OUT_RIGHT_MID,
                              lv_color_black());
                page_click_anim(pos_right, &Pages.page[Page_Home], anim_slide);
            }
            Param.ref_param &= ~Ref_Multi_Home;
            return;
        } else if (PageManager.main_page == &Pages.page[Page_Home]) {
            if (Param.ref_to_multi) {
                pm_creat_page(PageManager.main_page->pos[pos_left].page, PageManager.main_page, LV_ALIGN_OUT_LEFT_MID,
                              lv_color_black());
                page_click_anim(pos_left, &Pages.page[Page_Multi], anim_slide);
                Param.ref_to_multi = false;
            }
            Param.ref_param &= ~Ref_Multi_Home;
            return;
        } else if (PageManager.main_page == &Pages.page[Page_Group_Info]) {
            if (Param.ref_to_multi) {
                pm_creat_page(PageManager.main_page->pos[pos_left].page, PageManager.main_page, LV_ALIGN_OUT_LEFT_MID,
                              lv_color_black());
                page_click_anim(pos_left, &Pages.page[Page_Home], anim_slide);
            }
        }
    }

    if (Param.ref_param & Ref_Multi_Home) {
        return;
    }

    if (Param.ref_param & Ref_Level_Hz_Times) {
        if (Pages.page[Page_Home].state == page_created && Param.ctrl_type == type_flash) {
            for (int i = 0; i < GroupItemCount; ++i) {
                if (Param.group[i].is_added_to_list) {
                    lv_event_send(Param.group[i].slider_home.slider_bg, LV_EVENT_VALUE_CHANGED, NULL);
                }
            }
        } else if (Pages.page[Page_Multi].state == page_created) {
            uint8_t times_selected_index = binary_search_index(multi_times_arr, times_arr_size,
                                                               Param.multi.flash_times);
            uint8_t hz_selected_index = binary_search_index(multi_hz_arr, hz_arr_size, Param.multi.flash_freq);
            lv_roller_set_selected(Param.multi.times_roller, times_selected_index, LV_ANIM_OFF);
            lv_roller_set_selected(Param.multi.hz_roller, hz_selected_index, LV_ANIM_OFF);
            lv_event_send(Param.multi.slider, LV_EVENT_VALUE_CHANGED, NULL);
            // lv_event_send(Param.multi.times_roller, LV_EVENT_VALUE_CHANGED, NULL);
            // lv_event_send(Param.multi.hz_roller, LV_EVENT_VALUE_CHANGED, NULL);
        } else if (Pages.page[Page_Group_Info].state == page_created) {
            for (int i = 0; i < GroupItemCount; ++i) {
                if (Param.group[i].is_added_to_list) {
                    lv_event_send(Param.group[i].slider_option_flash_level.slider_bg, LV_EVENT_VALUE_CHANGED,
                                  NULL);
                }
            }
        }
    }

    if (Param.ref_param & Ref_Mode) {
        if (Pages.page[Page_Group_Info].state == page_created) {
            for (int i = 0; i < GroupItemCount; ++i) {
                if (Param.group[i].is_added_to_list) {
                    lv_event_send(Param.group[i].mode_layout, LV_EVENT_VALUE_CHANGED, NULL);
                }
            }
        } else if (Pages.page[Page_Home].state == page_created && Param.ctrl_type == type_flash) {
            for (int i = 0; i < GroupItemCount; ++i) {
                if (Param.group[i].is_added_to_list) {
                    lv_event_send(Param.group[i].slider_home.slider_bg, LV_EVENT_VALUE_CHANGED, NULL);
                }
            }
        }
    }
    Param.ref_param = Ref_None;
}

#endif

#if 0
void send_create_sleep_page() {
    Param.standby_obj = comp_sleep_init();
}

void send_del_sleep_page() {
    if (lv_obj_is_valid(Param.standby_obj)) {
        lv_obj_del_async(Param.standby_obj);
        Param.standby_obj = NULL;
    }
}
#endif
