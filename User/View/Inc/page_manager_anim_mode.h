//
// Created by <PERSON><PERSON> on 2024/6/15.
//

#ifndef LV_PORT_WIN_CODEBLOCKS_V8_PAGE_MANAGER_ANIM_MODE_H
#define LV_PORT_WIN_CODEBLOCKS_V8_PAGE_MANAGER_ANIM_MODE_H

#include "user.h"
#include "page_manager.h"

typedef enum {
    page_motion_direction_none,
    page_pull_down,
    page_pull_up,
    page_pull_left,
    page_pull_right
} PageMotionDirectionEnum;

typedef enum {
    slide_none,
    slide_horizontal,
    slide_vertical
} SlideDirectionEnumType;

typedef struct PageNode PageTypeHandle;

typedef struct {
    bool is_finished_anim;
    PageTypeHandle *target_page;
    PageTypeHandle *source_page;
    bool can_be_deleted;

    lv_obj_t *mask;
} AnimStructType;
extern AnimStructType Anim;

typedef struct {
    PositionEnum position;
    PageTypeHandle *target_page;
    lv_obj_t *parent_page;
} HandleStructType;

lv_obj_t *pm_create_handle(lv_obj_t *parent_page, PositionEnum position);

void page_click_anim(PositionEnum handle_pos, PageTypeHandle *page, uint8_t anim_mode);

void printf_main_page();

#endif //LV_PORT_WIN_CODEBLOCKS_V8_PAGE_MANAGER_ANIM_MODE_H
