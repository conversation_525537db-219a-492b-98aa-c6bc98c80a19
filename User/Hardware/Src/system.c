//
// Created by <PERSON><PERSON> on 2024/5/30.
//


#include "system.h"
#include "lvgl.h"
#include "examples/porting/lv_port_indev.h"
#include "examples/porting/lv_port_disp.h"
#include "amoled_qspi.h"
#include "demos/widgets/lv_demo_widgets.h"
#include "user.h"
#include "touch.h"
#include "adc_dma.h"
#include "rf_tx.h"
#include "rf_module.h"
#include "scan_key.h"
#include "rf_rx.h"
#include "encoder.h"
#include "nikon_protocol.h"
#include "view_model.h"
#include "flash.h"
#include "data_storage.h"
#include "bt_module.h"

TimePollInterval Time;
SystemStructType System;

// 待机降低亮度秒数
uint16_t standby_time[] = {15, 30, 60, 120, 180};
// 关机秒数
// {30 * 60s, 60 * 60s, 90 * 60s}
uint16_t power_off_time[] = {0xFF, 1800, 3600, 7200};

uint8_t first_run = false;

void HAL_IncTick(void) {
    uwTick += (uint32_t) uwTickFreq;
    lv_tick_inc(1);
    flash_multi_mode_int();
    Time.t1ms = 1;

    if (System.shut_led_status) {
        if (System.shut_led_countdown) {
            System.shut_led_countdown--;
        } else {
            LED_RED_WriteL;
            System.shut_led_status = false;
        }
    }
}

uint32_t g_fac_us = 0; /* us 延时倍乘数 */
uint32_t sw_1_us_count = 25;

/**
* @brief    初始化延时函数
* @param    无
* @retval   无
*/
void delay_init() {
    g_fac_us = HAL_RCC_GetHCLKFreq() / 1000000; //获取MCU的主频
    switch (g_fac_us) {
        case 250:
            sw_1_us_count = 25;
            break;
        case 50:
            sw_1_us_count = 3;
            break;
        default:
            break;
    }
}

/**
* @brief    us延时函数
* @param    nus:要延时的us数
* @note     nus取值范围：0 ~ (2^32 / fac_us)(fac_us一般等于系统主频)
* @retval   无
*/
void delay_us(uint32_t nus) {
    uint32_t ticks;
    uint32_t told, tnow, tcnt = 0;
    uint32_t reload = SysTick->LOAD; /*LOAD的值*/
    ticks = nus * g_fac_us;          /*需要的节拍数*/

    told = SysTick->VAL; /*刚进入时的计数器值*/
    while (1) {
        tnow = SysTick->VAL;
        if (tnow != told) {
            if (tnow < told) tcnt += told - tnow; /*注意一下SYSTICK是一个递减的计数器*/
            else tcnt += reload - tnow + told;    /*防止VAL减到0重装载了还没延时完*/
            told = tnow;
            if (tcnt >= ticks) break; /*时间超过/等于要延时的时间，则退出*/
        }
    }
}

void delay_us_sw(uint32_t count) {
    for (int i = 0; i < count; ++i) {
        uint32_t delay_count = sw_1_us_count;
        while (delay_count) {
            delay_count--;
        }
    }
}


// TIM6为通用定时器在APB1总线上，主频为240MHz
// TIM6工作频率 = 240000000 / 240 = 1000000Hz = 1MHz
// TIM6定时频率 = 1000000Hz / 65535 = 15.25902Hz
// TIM6定时周期 = 1 / 定时频率 = 1/15.25902 = 0.065535s = 65525us
void delay_us_tim6(uint32_t us) {
    // 左移6位即乘以 2^6=64，此时与TIM6的Prescaler对应
    // Prescaler = 1时，即主频/2，=0时即主频/1
    // us <<= 6;
    // 传入的值不可大于 65536 / 64 = 1024，不然会溢出
    __HAL_TIM_SET_COUNTER(&htim6, 65535 - us);
    __HAL_TIM_CLEAR_FLAG(&htim6, TIM_FLAG_UPDATE);

    HAL_TIM_Base_Start(&htim6);
    // 避免CEN被意外关闭，导致定时器CNT不继续计数，添加CEN状态判断条件
    while (__HAL_TIM_GET_FLAG(&htim6, TIM_FLAG_UPDATE) == RESET && (TIM6->CR1 & TIM_CR1_CEN));
    HAL_TIM_Base_Stop(&htim6);
}

void delay_ms(uint16_t count) {
    while (count--) {
        delay_us(1000);
    }
}

void SystemClock_Config_50M() {
    Disable_Interrupts;
    System.is_sys_clock_50M = true;
    // 150M
    // RCC_OscInitTypeDef RCC_OscInitStruct = {0};
    // RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};
    //
    // /** Configure the main internal regulator output voltage
    // */
    // __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE2);
    //
    // while (!__HAL_PWR_GET_FLAG(PWR_FLAG_VOSRDY)) {}
    //
    // // 先将时钟源选择为内部时钟
    // // https://blog.csdn.net/qq153471503/article/details/114700884
    // RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_SYSCLK;
    // RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_HSI;
    // if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_4) != HAL_OK) {
    //     Error_Handler();
    // }
    //
    // /** Initializes the RCC Oscillators according to the specified parameters
    // * in the RCC_OscInitTypeDef structure.
    // */
    // RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_LSI | RCC_OSCILLATORTYPE_HSE
    //                                    | RCC_OSCILLATORTYPE_CSI;
    // RCC_OscInitStruct.HSEState = RCC_HSE_ON;
    // RCC_OscInitStruct.LSIState = RCC_LSI_ON;
    // RCC_OscInitStruct.CSIState = RCC_CSI_ON;
    // RCC_OscInitStruct.CSICalibrationValue = RCC_CSICALIBRATION_DEFAULT;
    // RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
    // RCC_OscInitStruct.PLL.PLLSource = RCC_PLL1_SOURCE_HSE;
    // RCC_OscInitStruct.PLL.PLLM = 2;
    // RCC_OscInitStruct.PLL.PLLN = 24;
    // RCC_OscInitStruct.PLL.PLLP = 2;
    // RCC_OscInitStruct.PLL.PLLQ = 2;
    // RCC_OscInitStruct.PLL.PLLR = 2;
    // RCC_OscInitStruct.PLL.PLLRGE = RCC_PLL1_VCIRANGE_3;
    // RCC_OscInitStruct.PLL.PLLVCOSEL = RCC_PLL1_VCORANGE_WIDE;
    // RCC_OscInitStruct.PLL.PLLFRACN = 0;
    // if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK) {
    //     Error_Handler();
    // }
    //
    // /** Initializes the CPU, AHB and APB buses clocks
    // */
    // RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK | RCC_CLOCKTYPE_SYSCLK
    //                               | RCC_CLOCKTYPE_PCLK1 | RCC_CLOCKTYPE_PCLK2
    //                               | RCC_CLOCKTYPE_PCLK3;
    // RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
    // RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
    // RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV1;
    // RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;
    // RCC_ClkInitStruct.APB3CLKDivider = RCC_HCLK_DIV1;
    //
    // if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_4) != HAL_OK) {
    //     Error_Handler();
    // }
    //
    // /** Configure the programming delay
    // */
    // __HAL_FLASH_SET_PROGRAM_DELAY(FLASH_PROGRAMMING_DELAY_2);

    // 50M
    RCC_OscInitTypeDef RCC_OscInitStruct = {0};
    RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

    /** Configure the main internal regulator output voltage
    */
    __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE3);

    while (!__HAL_PWR_GET_FLAG(PWR_FLAG_VOSRDY)) {
    }

    // 先将时钟源选择为内部时钟
    // https://blog.csdn.net/qq153471503/article/details/114700884
    RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_SYSCLK;
    RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_HSI;
    if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_4) != HAL_OK) {
        Error_Handler();
    }

    /** Initializes the RCC Oscillators according to the specified parameters
    * in the RCC_OscInitTypeDef structure.
    */
    RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_LSI | RCC_OSCILLATORTYPE_HSE
                                       | RCC_OSCILLATORTYPE_CSI;
    RCC_OscInitStruct.HSEState = RCC_HSE_ON;
    RCC_OscInitStruct.LSIState = RCC_LSI_ON;
    RCC_OscInitStruct.CSIState = RCC_CSI_ON;
    RCC_OscInitStruct.CSICalibrationValue = RCC_CSICALIBRATION_DEFAULT;
    RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
    RCC_OscInitStruct.PLL.PLLSource = RCC_PLL1_SOURCE_HSE;
    RCC_OscInitStruct.PLL.PLLM = 2;
    RCC_OscInitStruct.PLL.PLLN = 16;
    RCC_OscInitStruct.PLL.PLLP = 2;
    RCC_OscInitStruct.PLL.PLLQ = 2;
    RCC_OscInitStruct.PLL.PLLR = 2;
    RCC_OscInitStruct.PLL.PLLRGE = RCC_PLL1_VCIRANGE_3;
    RCC_OscInitStruct.PLL.PLLVCOSEL = RCC_PLL1_VCORANGE_WIDE;
    RCC_OscInitStruct.PLL.PLLFRACN = 0;
    if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK) {
        Error_Handler();
    }

    /** Initializes the CPU, AHB and APB buses clocks
    */
    RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK | RCC_CLOCKTYPE_SYSCLK
                                  | RCC_CLOCKTYPE_PCLK1 | RCC_CLOCKTYPE_PCLK2
                                  | RCC_CLOCKTYPE_PCLK3;
    RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
    RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV2;
    RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV1;
    RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;
    RCC_ClkInitStruct.APB3CLKDivider = RCC_HCLK_DIV1;

    if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_4) != HAL_OK) {
        Error_Handler();
    }
    /** Configure the programming delay
    */
    __HAL_FLASH_SET_PROGRAM_DELAY(FLASH_PROGRAMMING_DELAY_2);

    // TIM6时钟切换到50M
    TIM_MasterConfigTypeDef sMasterConfig = {0};
    htim6.Instance = TIM6;
    htim6.Init.Prescaler = (50 - 1);
    htim6.Init.CounterMode = TIM_COUNTERMODE_UP;
    htim6.Init.Period = 65535;
    htim6.Init.AutoReloadPreload = TIM_AUTORELOAD_PRELOAD_DISABLE;
    if (HAL_TIM_Base_Init(&htim6) != HAL_OK) {
        Error_Handler();
    }
    sMasterConfig.MasterOutputTrigger = TIM_TRGO_RESET;
    sMasterConfig.MasterSlaveMode = TIM_MASTERSLAVEMODE_DISABLE;
    if (HAL_TIMEx_MasterConfigSynchronization(&htim6, &sMasterConfig) != HAL_OK) {
        Error_Handler();
    }
    delay_init();
    Enable_Interrupts;
}

void SystemClock_Config_250M() {
    Disable_Interrupts;
    System.is_sys_clock_50M = false;
    RCC_OscInitTypeDef RCC_OscInitStruct = {0};
    RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

    /** Configure the main internal regulator output voltage
    */
    __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE0);

    while (!__HAL_PWR_GET_FLAG(PWR_FLAG_VOSRDY)) {
    }

    // 先将时钟源选择为内部时钟
    // https://blog.csdn.net/qq153471503/article/details/114700884
    RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_SYSCLK;
    RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_HSI;
    if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_4) != HAL_OK) {
        Error_Handler();
    }

    /** Initializes the RCC Oscillators according to the specified parameters
    * in the RCC_OscInitTypeDef structure.
    */
    RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_LSI | RCC_OSCILLATORTYPE_HSE
                                       | RCC_OSCILLATORTYPE_CSI;
    RCC_OscInitStruct.HSEState = RCC_HSE_ON;
    RCC_OscInitStruct.LSIState = RCC_LSI_ON;
    RCC_OscInitStruct.CSIState = RCC_CSI_ON;
    RCC_OscInitStruct.CSICalibrationValue = RCC_CSICALIBRATION_DEFAULT;
    RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
    RCC_OscInitStruct.PLL.PLLSource = RCC_PLL1_SOURCE_HSE;
    RCC_OscInitStruct.PLL.PLLM = 2;
    RCC_OscInitStruct.PLL.PLLN = 40;
    RCC_OscInitStruct.PLL.PLLP = 2;
    RCC_OscInitStruct.PLL.PLLQ = 2;
    RCC_OscInitStruct.PLL.PLLR = 2;
    RCC_OscInitStruct.PLL.PLLRGE = RCC_PLL1_VCIRANGE_3;
    RCC_OscInitStruct.PLL.PLLVCOSEL = RCC_PLL1_VCORANGE_WIDE;
    RCC_OscInitStruct.PLL.PLLFRACN = 0;
    if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK) {
        Error_Handler();
    }

    /** Initializes the CPU, AHB and APB buses clocks
    */
    RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK | RCC_CLOCKTYPE_SYSCLK
                                  | RCC_CLOCKTYPE_PCLK1 | RCC_CLOCKTYPE_PCLK2
                                  | RCC_CLOCKTYPE_PCLK3;
    RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
    RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
    RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV1;
    RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;
    RCC_ClkInitStruct.APB3CLKDivider = RCC_HCLK_DIV1;

    if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_5) != HAL_OK) {
        Error_Handler();
    }

    /** Configure the programming delay
    */
    __HAL_FLASH_SET_PROGRAM_DELAY(FLASH_PROGRAMMING_DELAY_2);

    // TIM6时钟切换到250M
    TIM_MasterConfigTypeDef sMasterConfig = {0};

    /* USER CODE BEGIN TIM6_Init 1 */

    /* USER CODE END TIM6_Init 1 */
    htim6.Instance = TIM6;
    htim6.Init.Prescaler = (250 - 1);
    htim6.Init.CounterMode = TIM_COUNTERMODE_UP;
    htim6.Init.Period = 65535;
    htim6.Init.AutoReloadPreload = TIM_AUTORELOAD_PRELOAD_DISABLE;
    if (HAL_TIM_Base_Init(&htim6) != HAL_OK) {
        Error_Handler();
    }
    sMasterConfig.MasterOutputTrigger = TIM_TRGO_RESET;
    sMasterConfig.MasterSlaveMode = TIM_MASTERSLAVEMODE_DISABLE;
    if (HAL_TIMEx_MasterConfigSynchronization(&htim6, &sMasterConfig) != HAL_OK) {
        Error_Handler();
    }

    MX_USART3_UART_Init();
    delay_init();
    Enable_Interrupts;
}

void SystemClock_Config_8M() {
    Disable_Interrupts;
    System.is_sys_clock_50M = true;
    RCC_OscInitTypeDef RCC_OscInitStruct = {0};
    RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

    /** Configure the main internal regulator output voltage
    */
    __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE3);

    while (!__HAL_PWR_GET_FLAG(PWR_FLAG_VOSRDY)) {
    }

    // 先将时钟源选择为内部时钟
    // https://blog.csdn.net/qq153471503/article/details/114700884
    RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_SYSCLK;
    RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_HSI;
    if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_4) != HAL_OK) {
        Error_Handler();
    }

    /** Initializes the RCC Oscillators according to the specified parameters
    * in the RCC_OscInitTypeDef structure.
    */
    RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSI | RCC_OSCILLATORTYPE_LSI
                                       | RCC_OSCILLATORTYPE_CSI;
    RCC_OscInitStruct.HSIState = RCC_HSI_ON;
    RCC_OscInitStruct.HSIDiv = RCC_HSI_DIV1;
    RCC_OscInitStruct.HSICalibrationValue = RCC_HSICALIBRATION_DEFAULT;
    RCC_OscInitStruct.LSIState = RCC_LSI_ON;
    RCC_OscInitStruct.CSIState = RCC_CSI_ON;
    RCC_OscInitStruct.CSICalibrationValue = RCC_CSICALIBRATION_DEFAULT;
    RCC_OscInitStruct.PLL.PLLState = RCC_PLL_NONE;
    if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK) {
        Error_Handler();
    }

    /** Initializes the CPU, AHB and APB buses clocks
    */
    RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK | RCC_CLOCKTYPE_SYSCLK
                                  | RCC_CLOCKTYPE_PCLK1 | RCC_CLOCKTYPE_PCLK2
                                  | RCC_CLOCKTYPE_PCLK3;
    RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_HSI;
    RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV8;
    RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV1;
    RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;
    RCC_ClkInitStruct.APB3CLKDivider = RCC_HCLK_DIV1;

    if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_3) != HAL_OK) {
        Error_Handler();
    }

    /** Configure the programming delay
    */
    __HAL_FLASH_SET_PROGRAM_DELAY(FLASH_PROGRAMMING_DELAY_1);
    delay_init();
    Enable_Interrupts;
}

void exit_standby() {
    if (!System.is_standby) {
        return;
    }
    // exit_standby();
    // GPIO_InitTypeDef GPIO_InitStruct = {0};
    // SystemClock_Config();
    // System.is_sys_clock_50M = false;
    SystemClock_Config_50M();
    HAL_ResumeTick();
    System.is_standby = false;
    HAL_LPTIM_TimeOut_Stop_IT(&hlptim1);
    delay_ms(5);
    // Test_1_WriteL;

    GPIO_InitTypeDef GPIO_InitStruct = {0};
    GPIO_InitStruct.Pin = KEY_MODE_Pin | KEY_ENTER_Pin | KEY_FLASH_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);

    GPIO_InitStruct.Pin = GPIO_PIN_12 | GPIO_PIN_13;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);

    GPIO_InitStruct.Pin = CHG_OK_EXTI4_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_IT_RISING_FALLING;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    HAL_GPIO_Init(CHG_OK_EXTI4_GPIO_Port, &GPIO_InitStruct);

    GPIO_InitStruct.Pin = WKUP_EXTI0_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_IT_RISING_FALLING;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    HAL_GPIO_Init(WKUP_EXTI0_GPIO_Port, &GPIO_InitStruct);
    // data = 0x20;
    // amoled_write(0xFE, &data, 1);
    // data = 0x5A;
    // amoled_write(0xF4, &data, 1);
    // data = 0x59;
    // amoled_write(0xF5, &data, 1);
    //
    // data = 0x70;
    // amoled_write(0xFE, &data, 1);
    // data = 0x04;
    // amoled_write(0x26, &data, 1);
    //
    // data = 0x20;
    // amoled_write(0xFE, &data, 1);
    // data = 0xA5;
    // amoled_write(0xF4, &data, 1);
    // data = 0xA5;
    // amoled_write(0xF5, &data, 1);
    //
    // data = 0x20;
    // amoled_write(0xFE, &data, 1);
    // amoled_write(0x47, 0x00, 0);
    // amoled_write(0x38, 0x00, 0);
    // amoled_write(0x48, 0x00, 0);
    // __HAL_RCC_GPIOC_CLK_ENABLE();
    // __HAL_RCC_GPIOH_CLK_ENABLE();
    // __HAL_RCC_GPIOA_CLK_ENABLE();
    // __HAL_RCC_GPIOB_CLK_ENABLE();
    // __HAL_RCC_GPIOD_CLK_ENABLE();
    // __HAL_RCC_GPIOE_CLK_ENABLE();
    // __HAL_RCC_OSPI1_CLK_ENABLE();
    // __HAL_RCC_TIM6_CLK_ENABLE();
    // __HAL_RCC_I2C1_CLK_ENABLE();
    // __HAL_RCC_SPI1_CLK_ENABLE();


    // MX_GPIO_Init();
    // MX_GPDMA1_Init();
    // MX_OCTOSPI1_Init();
    // MX_ICACHE_Init();
    // MX_TIM6_Init();
    // MX_I2C1_Init();
    // MX_DCACHE1_Init();
    // MX_USART3_UART_Init();
    // MX_DCACHE1_Init();
    // HAL_ICACHE_Enable();
    // HAL_DMA_Init(&handle_GPDMA1_Channel0);
    MX_ADC1_Init();
    // HAL_ADC_MspInit(&hadc1);
    MX_SPI1_Init();
    MX_USART3_UART_Init();
    // HAL_NVIC_EnableIRQ(EXTI1_IRQn);
    HAL_NVIC_EnableIRQ(EXTI4_IRQn);
    // HAL_NVIC_EnableIRQ(EXTI5_IRQn);
    HAL_NVIC_EnableIRQ(EXTI6_IRQn);
    HAL_NVIC_DisableIRQ(EXTI14_IRQn);
    // HAL_NVIC_EnableIRQ(EXTI8_IRQn);
    // HAL_NVIC_EnableIRQ(EXTI11_IRQn);
    // HAL_NVIC_EnableIRQ(EXTI15_IRQn);
    // HAL_NVIC_EnableIRQ(ADC1_IRQn);
    // HAL_NVIC_EnableIRQ(I2C1_EV_IRQn);
    // HAL_NVIC_EnableIRQ(OCTOSPI1_IRQn);
    HAL_NVIC_EnableIRQ(GPDMA1_Channel0_IRQn);
    // cc2500_init_pgr();
    HAL_NVIC_EnableIRQ(GPDMA1_Channel1_IRQn);
    HAL_NVIC_EnableIRQ(LPTIM2_IRQn);
    HAL_NVIC_EnableIRQ(USART3_IRQn);
    // 避免没成功修改屏幕亮度
    delay_ms(5);
    // amoled_set_brightness_level(Param.brightness.level);
    cc2500_set_mode(RF_stby_mode);
    adc_start();
    set_nikon_MOSI_EXTI_input();
    CAM_COMM_WriteH;
    System.standby_countdown = standby_time[Setting.roller_values[roller_setting_standby]];
    // cc2500_set_mode(RF_rx_mode);
    // Enable_Interrupts;

}

void standby_pgr() {
    cc2500_set_mode(RF_sleep_mode);
    HAL_Delay(5);
    CAM_COMM_WriteL;
    PA_TX_EN_WriteL;
    PA_RX_EN_WriteL;
    LED_RED_WriteL;
    // amoled_write(0x10, 0x00, 0);
    HAL_Delay(5);
    // amoled_write(0xFE, 0x00, 0);
    // amoled_write(0x39, 0x00, 0);
    // amoled_write(0x49, 0x00, 0);
    // amoled_write(0x46, 0x00, 0);
    HAL_ADC_Stop_DMA(&hadc1);
    HAL_ADC_DeInit(&hadc1);
    HAL_SPI_DeInit(&hspi1);
    HAL_UART_DeInit(&huart3);
    // HAL_DMA_DeInit(&handle_GPDMA1_Channel0);
    // HAL_DMA_DeInit(&handle_GPDMA1_Channel1);
    // HAL_ICACHE_Disable();
    // HAL_DCACHE_DeInit(&hdcache1);
    // uint8_t data = 0x20;
    // amoled_write(0xFE, &data, 1);
    // data = 0x5A;
    // amoled_write(0xF4, &data, 1);
    // data = 0x59;
    // amoled_write(0xF5, &data, 1);
    //
    // data = 0x40;
    // amoled_write(0xFE, &data, 1);
    // data = 0x02;
    // amoled_write(0x61, &data, 1);
    // data = 0x07;
    // amoled_write(0x62, &data, 1);
    //
    // data = 0x70;
    // amoled_write(0xFE, &data, 1);
    // data = 0x88;
    // amoled_write(0x26, &data, 1);
    //
    // data = 0x20;
    // amoled_write(0xFE, &data, 1);
    // data = 0xA5;
    // amoled_write(0xF4, &data, 1);
    // data = 0xA5;
    // amoled_write(0xF5, &data, 1);

    // HAL_NVIC_DisableIRQ(EXTI1_IRQn);
    // HAL_NVIC_DisableIRQ(EXTI4_IRQn);
    // HAL_NVIC_DisableIRQ(EXTI5_IRQn);
    // HAL_NVIC_DisableIRQ(EXTI6_IRQn);
    // HAL_NVIC_DisableIRQ(EXTI8_IRQn);
    // HAL_NVIC_DisableIRQ(EXTI11_IRQn);
    // HAL_NVIC_DisableIRQ(EXTI15_IRQn);
    // HAL_NVIC_DisableIRQ(ADC1_IRQn);
    // HAL_NVIC_DisableIRQ(I2C1_EV_IRQn);
    // HAL_NVIC_DisableIRQ(OCTOSPI1_IRQn);

    HAL_NVIC_DisableIRQ(GPDMA1_Channel0_IRQn);
    HAL_NVIC_DisableIRQ(GPDMA1_Channel1_IRQn);
    HAL_NVIC_DisableIRQ(LPTIM2_IRQn);

    // HAL_GPIO_DeInit(GPIOC, KEY_ENTER_Pin);
    // HAL_GPIO_DeInit(GPIOC, KEY_FLASH_Pin);
    // HAL_GPIO_DeInit(WKUP_EXTI0_GPIO_Port, WKUP_EXTI0_Pin);

    GPIO_InitTypeDef GPIO_InitStruct = {0};
    GPIO_InitStruct.Pin = KEY_ENTER_Pin | KEY_MODE_Pin | KEY_FLASH_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_IT_FALLING;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);

    GPIO_InitStruct.Pin = GPIO_PIN_0 | GPIO_PIN_1;
    GPIO_InitStruct.Mode = GPIO_MODE_ANALOG;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    HAL_GPIO_Init(GPIOH, &GPIO_InitStruct);

    GPIO_InitStruct.Pin = GPIO_PIN_12 | GPIO_PIN_13 | GPIO_PIN_14;
    GPIO_InitStruct.Mode = GPIO_MODE_ANALOG;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);

    GPIO_InitStruct.Pin = CHG_OK_EXTI4_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_ANALOG;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    HAL_GPIO_Init(CHG_OK_EXTI4_GPIO_Port, &GPIO_InitStruct);

    GPIO_InitStruct.Pin = WKUP_EXTI0_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_IT_RISING;
    GPIO_InitStruct.Pull = GPIO_PULLDOWN;
    HAL_GPIO_Init(WKUP_EXTI0_GPIO_Port, &GPIO_InitStruct);

    GPIO_InitStruct.Pin = NIKON_MOSI_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_IT_FALLING;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);

    // GPIO_InitStruct.Pin = GPIO_PIN_11 | GPIO_PIN_10;
    // GPIO_InitStruct.Mode = GPIO_MODE_ANALOG;
    // GPIO_InitStruct.Pull = GPIO_NOPULL;
    // HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);
    //
    // GPIO_InitStruct.Pin = GPIO_PIN_5 | GPIO_PIN_6 | GPIO_PIN_7;
    // GPIO_InitStruct.Mode = GPIO_MODE_ANALOG;
    // GPIO_InitStruct.Pull = GPIO_NOPULL;
    // HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

    GPIO_InitStruct.Pin = KEY_FLASH_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_IT_FALLING;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);

    // GPIO_InitStruct.Pin = WKUP_EXTI0_Pin;
    // GPIO_InitStruct.Mode = GPIO_MODE_IT_RISING;
    // GPIO_InitStruct.Pull = GPIO_NOPULL;
    // HAL_GPIO_Init(WKUP_EXTI0_GPIO_Port, &GPIO_InitStruct);

    HAL_NVIC_EnableIRQ(EXTI0_IRQn);
    // 用于KEY_MODE唤醒
    HAL_NVIC_EnableIRQ(EXTI14_IRQn);
    HAL_NVIC_EnableIRQ(EXTI15_IRQn);
    HAL_NVIC_DisableIRQ(USART3_IRQn);
    HAL_NVIC_DisableIRQ(EXTI6_IRQn);
    HAL_NVIC_DisableIRQ(EXTI4_IRQn);

    delay_ms(3);
    // delay_us(10);

    // Disable_Interrupts;
    // for (int i = 0; i <= 130; ++i) {
    //     HAL_NVIC_DisableIRQ(i);
    // }
    // // HAL_NVIC_DisableIRQ(i);
    // __disable_irq();

    // __HAL_RCC_DBGMCU_CLK_ENABLE();
    // HAL_DBGMCU_EnableDBGStopMode();
    if (Setting.roller_values[roller_setting_off]) {
        System.off_count = Setting.roller_values[roller_setting_off] * 10;
        // System.off_count = Setting.roller_values[roller_setting_off];
        do {
            System.is_from_lptim = false;
            // Test_1_WriteH;
            // 1s 128分频，一次计数4ms，250为1s，3分钟为45000
            HAL_LPTIM_TimeOut_Start_IT(&hlptim1, 45000);
            // HAL_LPTIM_TimeOut_Start_IT(&hlptim1, 500);
            HAL_SuspendTick();
            HAL_PWR_EnterSTOPMode(PWR_LOWPOWERREGULATOR_ON, PWR_STOPENTRY_WFI);
            // LED_RED_WriteH;
        } while (System.is_from_lptim);
        exit_standby();
        // SystemClock_Config();
        // HAL_ResumeTick();
        if (System.is_off) {
            device_deinit();
        }
        // Test_1_WriteH;
        // HAL_LPTIM_TimeOut_Start_IT(&hlptim1, 1250);
    } else {
        HAL_SuspendTick();
        HAL_PWR_EnterSTOPMode(PWR_LOWPOWERREGULATOR_ON, PWR_STOPENTRY_WFI);
        // LED_RED_WriteH;
        // SystemClock_Config();
        // HAL_ResumeTick();
    }
    exit_standby();
}

void print_RST_flag() {
    if (__HAL_RCC_GET_FLAG(RCC_FLAG_PINRST) != RESET) // NRST 引脚复位
    {
        printf("PIN reset \r\n");
    }
    if (__HAL_RCC_GET_FLAG(RCC_FLAG_BORRST) != RESET) // 上电掉电复位
    {
        printf("BOR reset \r\n");
    }
    if (__HAL_RCC_GET_FLAG(RCC_FLAG_SFTRST) != RESET) //  软件复位
    {
        printf("Software reset  \r\n");
    }
    if (__HAL_RCC_GET_FLAG(RCC_FLAG_IWDGRST) != RESET) // 独立看门狗复位
    {
        printf("Independent watchdog reset \r\n");
    }
    if (__HAL_RCC_GET_FLAG(RCC_FLAG_WWDGRST) != RESET) // 窗口看门狗复位
    {
        printf("Window watchdog reset \r\n");
    }
    if (__HAL_RCC_GET_FLAG(RCC_FLAG_LPWRRST) != RESET) // 低功耗复位
    {
        printf("(Low-power reset \r\n");
    }
    // __HAL_RCC_CLEAR_RESET_FLAGS();
    printf("\r\n");
}


bool check_SW_RST_flag() {
    // 如果是软件复位
    if (__HAL_RCC_GET_FLAG(RCC_FLAG_SFTRST) != RESET) {
        __HAL_RCC_CLEAR_RESET_FLAGS();
        return true;
    } else {
        return false;
    }
}

bool check_WDG_RST_flag() {
    // 如果是WWDG复位
    if (__HAL_RCC_GET_FLAG(RCC_FLAG_WWDGRST) != RESET) {
        __HAL_RCC_CLEAR_RESET_FLAGS();
        return true;
    } else {
        return false;
    }
}

/**
 * @brief 重置数据并保存
 */
void data_reset() {
    uint8_t temp_lang = Setting.values[setting_language];
    fill_default_data();
    Setting.values[setting_language] = temp_lang;
    System.res_to = res_to_main_page;
    storage_save_data();
}

// 窗口看门狗中断回调函数
// void HAL_WWDG_EarlyWakeupCallback(WWDG_HandleTypeDef *hwwdg) {
//     if (!System.do_not_refresh_WWDG) {
//         // 刷新看门狗
//         HAL_WWDG_Refresh(hwwdg);
//     }
// }

/**
 * @brief 软复位(重启)
 * 重置页
 * 充电页长按开机进正常页面
 * 插电状态正常页面关机复位跳回充电页
 */
void device_sw_reset() {
    amoled_enter_sleep();
    if (__HAL_RCC_GET_FLAG(RCC_FLAG_SFTRST) != RESET) {
        __HAL_RCC_CLEAR_RESET_FLAGS();
    }
    delay_us(2000);

    // 从 SYSRESETREQ 被置为有效，到复位发生器执行复位命令，往往会有一个延时。在此延时期间，处理器仍然可以响应中断请求。
    // 关闭所有中断
    Disable_Interrupts;
    // 复位
    HAL_NVIC_SystemReset();
}

// 设备反初始化(关机)
void device_deinit() {
    // 保存数据
    storage_save_data();
    // RF休眠
    cc2500_set_mode(RF_sleep_mode);
    // 显示屏休眠
    amoled_enter_sleep();
    // 触控休眠
    cst820_sleep();
    // 显示屏深度休眠
    uint8_t data = 0x01;
    amoled_write(0x4F, &data, 1);
    if (__HAL_PWR_GET_FLAG(PWR_FLAG_SBF) != RESET) {
        /* Clear Standby flag */
        __HAL_PWR_CLEAR_FLAG(PWR_FLAG_SBF);
        /* Check and Clear the Wakeup flag */
        if (__HAL_PWR_GET_FLAG(PWR_WAKEUP_FLAG1) != RESET) {
            __HAL_PWR_CLEAR_FLAG(PWR_WAKEUP_FLAG1);
        }
    }
    // 必须延时800
    delay_us(2000);
    // delay_ms(5);

    BOOST_EN_WriteL;
    LED_RED_WriteL;
    CAM_COMM_WriteL;

    HAL_PWR_DisableWakeUpPin(PWR_WAKEUP_PIN1);
    __HAL_PWR_CLEAR_FLAG(PWR_WAKEUP_FLAG1);
    // 带_HIGH表示上升沿触发，带_LOW表示下降沿触发。
    HAL_PWR_EnableWakeUpPin(PWR_WAKEUP_PIN1_HIGH);
    HAL_PWR_EnterSTANDBYMode();
}

// bool is_pressing_switch_key = false;
// static uint8_t power_key_off_count;

// void check_power_key() {
//     if (is_pressing_switch_key) {
//         if (!System.switch_status) {
//             // while (1) {
//             //     if (!MAIN_SWITCH_Read && System.power_key_press_count >= 1000) {
//             //         break;
//             //     }
//             // }
//             // HAL_Delay(10);
//             // BOOST_EN_WriteH;
//             // System.switch_status = true;
//         } else {
//             if (!MAIN_SWITCH_Read) {
//                 power_key_off_count++;
//                 if (power_key_off_count >= 20) {
//                     device_deinit();
//                 }
//             } else {
//                 power_key_off_count = 0;
//             }
//         }
//     }
// }

void time_slice_1ms() {
    if (MAIN_SWITCH_Read) {
        System.from_MAIN_SWITCH_INT = 0;
    }
    if (!Setting.values[setting_shoot]) {
        Nikon_SPI_Flag_Prog();
    } else {
        set_nikon_SCK_output();
        Nikon_SCK_WriteH;
        Param.status_bar_sync = sync_front;
        Nikon.has_link_flag = false;
        send_camera_link_status();
    }
    adc_cal();
    cc2500_scan_status();
    // scan_encoder_pgr();
    scan_key_pgr();
    // 用时50-80ms
    lv_task_handler();
    // 检测涓流充电
    battery_CHG_OK_check();
}

void time_slice_10ms() {
    cc2500_scan_ch_rssi();
    battery_charge_check();
    rf_send_from_ui();
    if (Nikon_SDA_Read) {
        System.nikon_sda_is_falling = false;
    }
    if (Param.is_charging_page) {
        if (!WKUP_EXTI0_Read && MAIN_SWITCH_Read && !ChargingPage.is_low_anim) {
            device_deinit();
        }
        if (WKUP_EXTI0_Read && MAIN_SWITCH_Read && ChargingPage.is_low_anim) {
            // 低电动画时插电重置跳回充电页
            // 置起强制跳充电页标志，并存储数据
            if (!Param.is_charging_page) {
                System.res_to = res_to_charging_page;
                // 保存数据
                storage_save_data();
            }
            device_sw_reset();
        }
    }

}

void time_slice_50ms() {
    // System.indev_group = lv_group_get_default();
    // check_power_key();
}

void time_slice_100ms() {

}

// lv_obj_t *sleep_obj;
void time_slice_500ms() {
    // if (Setting.roller_values[roller_setting_id] == 99 && Setting.roller_values[roller_setting_ch] == 32) {
    //     rf_send_ch_id_0xED_0x01();
    // }
    // #include "lvgl_custom_function.h"
    // LV_IMG_DECLARE(sleep)
    // if(!lv_obj_is_valid(sleep_obj)){
    //     sleep_obj = lv_obj_custom_trans_create(lv_layer_top(),368,448);
    //     lv_obj_set_style_bg_color(sleep_obj,lv_color_black(),0);
    //     lv_obj_set_style_bg_opa(sleep_obj,LV_OPA_COVER,0);
    //     lv_obj_t *a = lv_img_custom_create(sleep_obj,&sleep);
    //     lv_obj_center(a);
    // }
}

void time_slice_1000ms() {
    if (!first_run) {
        if (!Param.is_charging_page && Pages.page[Page_Welcome].state != page_created) {
            first_run = true;
            CAM_COMM_WriteH;
        }
    }

    if (System.is_connecting && System.last_brightness_level == 0xFF) {
        System.standby_countdown = standby_time[Setting.roller_values[roller_setting_standby]];
        System.is_connecting = false;
    }
    // Test_1_WriteH;
    // delay_us_sw(8);
    // Test_1_WriteL;
    // 低亮度待机处理
    if (System.standby_countdown) {
        // amoled_keep_alive();
        // pa_tx_on();
        // BT_EN_WriteL;
        // LED_RED_WriteH;
        System.standby_countdown--;

        // if (System.last_brightness_level != Param.brightness.level) {
        //     amoled_set_brightness_level(Param.brightness.level);
        //     System.last_brightness_level = Param.brightness.level;
        // }
    } else {
        // 倒计时后设置最低亮度,进入待机模式
        if (System.last_brightness_level <= 100) {
            uint8_t data = 0x00;
            amoled_write(0xFE, &data, 1);
            delay_us(40);
            amoled_set_brightness_code(0x1A);
            System.last_brightness_level = 0xFF;
            delay_us(40);
            amoled_write(0x39, 0x00, 0);
            SystemClock_Config_50M();
            // delay_us(40);
            // amoled_write(0x49, 0x00, 0);
        }
        if (!Nikon.has_link_flag) {
            if (!WKUP_EXTI0_Read && MAIN_SWITCH_Read) {
                // 降低亮度30s后进低功耗
                if (!System.is_standby) {
                    HAL_LPTIM_TimeOut_Start_IT(&hlptim2, 250);
                    // Test_1_WriteH;
                }
            } else {
                HAL_LPTIM_TimeOut_Stop_IT(&hlptim2);
            }
        }

    }

    if (System.is_standby) {
        // if (lv_obj_is_valid(Param.standby_obj)) {
        //     standby_pgr();
        //     send_del_sleep_page();
        // } else {
        //     send_create_sleep_page();
        //     HAL_LPTIM_TimeOut_Stop_IT(&hlptim2);
        // }
        HAL_LPTIM_TimeOut_Stop_IT(&hlptim2);
        standby_pgr();
    }

    // // 自动关机处理
    // if (System.power_off_countdown) {
    //     if (System.power_off_countdown != 0xFF) {
    //         System.power_off_countdown--;
    //     }
    // } else {
    //     // 倒计时到达后关机
    //     device_deinit();
    // }

    // 超时失焦处理
    if (System.defocused_countdown) {
        System.defocused_countdown--;
    } else {
        defocused_handle(NULL);
    }

    if (Param.is_wireless_sync) {
        rf_send_ch_id_0xED_0x01();
    }
    // 非充电页10秒保存一次
    if (!Param.is_charging_page) {
        if (System.save_data_countdown >= 10) {
            // 保存数据
            storage_save_data();
            System.save_data_countdown = 0;
        } else {
            System.save_data_countdown++;
        }
    }

    // char *sleep_at = "AT+NAME=?";
    // bt_send_cmd((uint8_t *) sleep_at, strlen(sleep_at));
}

// 时间轮询
void time_poll_interval() {
    while (1) {
        // 等待1ms完成，以校准时间
        while (Time.t1ms == 0) {
            // HAL_GPIO_TogglePin(TEST_1_GPIO_Port, TEST_1_Pin);
            __NOP();
        }

        time_slice_1ms();
        Time.t1ms = 0;

        Time.t10ms++;
        if (Time.t10ms >= 10) {
            time_slice_10ms();
            Time.t10ms = 0;

            Time.t50ms++;
            if (Time.t50ms >= 5) {
                time_slice_50ms();
                Time.t50ms = 0;

                Time.t100ms++;
                if (Time.t100ms >= 2) {
                    time_slice_100ms();
                    Time.t100ms = 0;

                    Time.t500ms++;
                    if (Time.t500ms >= 5) {
                        time_slice_500ms();
                        Time.t500ms = 0;

                        Time.t1000ms++;
                        if (Time.t1000ms >= 2) {
                            time_slice_1000ms();
                            Time.t1000ms = 0;
                        }
                    }
                }
            }
        }
    }
}

void user_pgr() {
    Enable_Interrupts;
    CAM_COMM_WriteL;
    BOOST_EN_WriteL;
    print_RST_flag();
    bool is_sw_rst = check_SW_RST_flag();
    bool usb_is_plugged = false;
    if (!MAIN_SWITCH_Read) {
        // 记录进入程序后MAIN_SWITCH仍被按下
        // 用于避免松手产生的WKUP下降沿误判为USB拔出
        System.MAIN_SWITCH_is_pressed = 1;
    }
    if (WKUP_EXTI0_Read && MAIN_SWITCH_Read) {
        usb_is_plugged = true;
    } else {
        usb_is_plugged = false;
    }
    Param.electricity_level = 0xFF;
    // 读取数据
    storage_read_data();
    adc_start();
    // 第一次上电要把VREFINT_CAL读回来
    get_vrefint_cal();
    if (System.res_to != res_to_main_page_no_welcome) {
        for (uint8_t i = 0; i < 100; i++) {
            HAL_Delay(1);
            get_adc();
        }
        adc_cal();
        for (uint8_t i = 0; i < 200; ++i) {
            battery_CHG_OK_check();
            battery_charge_check();
            HAL_Delay(1);
        }

        if (ADC_DMA.vol_bat < BAT_LEVEL_OFF - 170) {
            // HAL_LPTIM_TimeOut_Start_IT(&hlptim1, 250);
            // HAL_ADC_Stop_DMA(&hadc1);
            // Disable_Interrupts;
            // HAL_SuspendTick();
            // HAL_PWR_EnterSTOPMode(PWR_LOWPOWERREGULATOR_ON, PWR_STOPENTRY_WFI);
            device_sw_reset();
        }
    } else {
        Param.battery_status = battery_normal;
        Param.last_battery_status = Param.battery_status;
        Param.electricity_level = 3;
    }
    // else
    // if (ADC_DMA.vol_bat < BAT_LEVEL_OFF) {
    // System.is_low_bat = true;
    // }
    SystemClock_Config_250M();
    BOOST_EN_WriteH;
    // lvgl初始化
    lv_init();
    lv_port_disp_init();
    lv_port_indev_init();
    // 屏幕初始化
    amoled_init();
    // 触控初始化
    cst820_init();
    LV_LOG_USER("4");
    // touch_init();
    // extern void lv_demo_benchmark(void);
    // lv_demo_benchmark();
    // extern void lv_demo_widgets(void);
    // lv_demo_widgets();
    if (!is_sw_rst || System.res_to == res_to_charging_page) {
        if (System.res_to != res_to_main_page) {
            if (usb_is_plugged || Param.battery_status >= battery_low) {
                if (Param.battery_status == battery_normal) {
                    Param.battery_status = battery_charging;
                }
                // else if (ADC_DMA.vol_bat < BAT_LEVEL_OFF) {
                //     显示屏休眠
                // amoled_enter_sleep();
                // }
                // 充电页标志
                Param.is_charging_page = true;
                // 关闭快门中断
                HAL_NVIC_DisableIRQ(EXTI1_IRQn);
                // 关闭编码器中断
                HAL_NVIC_DisableIRQ(EXTI8_IRQn);
                // 关闭RF中断
                HAL_NVIC_DisableIRQ(EXTI5_IRQn);
                // 关闭通讯中断
                HAL_NVIC_DisableIRQ(EXTI15_IRQn);
                LV_LOG_USER("5");
                set_nikon_SCK_output();
                Nikon_SCK_WriteH;
                amoled_set_brightness_level(CHG_PAGE_AMOLED_LEVEL);
                if (Param.battery_status == battery_charging) {
                    // 待机时间改为30s
                    System.standby_countdown = CHG_ING_STANDBY_TIME;
                } else {
                    System.standby_countdown = CHG_OK_STANDBY_TIME;
                }

                // 取消自动关机
                System.power_off_countdown = 0xFF;
                page_charging_init();
            }
        }
    }

    LV_LOG_USER("6");

    if (!Param.is_charging_page) {
        System.standby_countdown = standby_time[Setting.roller_values[roller_setting_standby]];
        System.power_off_countdown = power_off_time[Setting.roller_values[roller_setting_off]];
        // CAM_COMM_WriteH;
        user_main();
        if (WKUP_EXTI0_Read) {
            // 不使用定时器延时会导致动画异常
            delay_us_tim6(800 * 1000);
        }
        if (!WKUP_EXTI0_Read && System.res_to < res_to_main_page) {
            device_deinit();
        }
    }
    // System.last_brightness_level = Param.brightness.level;
    Encoder.defocused_flag = true;
    System.defocused_countdown = ENCODER_NO_ACTION_TIME;
    // 清除强制跳充电页标志位
    System.res_to = res_to_default;
    // 保存数据，避免充电页不保存数据，无法更新 Flash 中的 System.res_to
    storage_save_data();
    // lv_example_control_center();
    // lv_obj_set_style_bg_color(lv_scr_act(), lv_color_white(), LV_PART_MAIN);
    // lv_obj_set_style_bg_opa(lv_scr_act(), LV_OPA_COVER, LV_PART_MAIN);
    // __HAL_RCC_CLEAR_RESET_FLAGS();
    // __HAL_TIM_CLEAR_IT(&htim7, TIM_IT_UPDATE);   /*清除更新中断标志，避免定时器一启动就进入更新中断*/
    // HAL_TIM_Base_Start_IT(&htim7);  /*开启定时器中断*/
    HAL_NVIC_DisableIRQ(EXTI14_IRQn);

    BT_EN_WriteH;
    delay_ms(5);
    BT_EN_WriteL;
    delay_ms(5);
    BT_EN_WriteH;
    delay_ms(5);

    // delay_us(2000);
    // BT_EN_WriteL;
    // bt_receive_data();

    time_poll_interval();
}

// lv_disp_t *disp_my;

void HAL_GPIO_EXTI_Falling_Callback(uint16_t GPIO_Pin) {
    // if (System.is_standby) {
    //     return;
    // }
    if (GPIO_Pin == NIKON_MOSI_Pin) {
        if (!first_run) {
            return;
        }
        delay_us(10);
        if (!Nikon_SDA_Read) {
            // Test_1_WriteH;
            // Test_1_WriteL;
            System.nikon_sda_is_falling = true;
            Nikon_SPI_TTL_Prog();
        }
    } else if (GPIO_Pin == FLASH_TRI_Pin) {
        if (!first_run) {
            return;
        }
        delay_us(3);
        if (!FLASH_TRI_Read) {
            System.is_connecting = true;
            if (Setting.values[setting_shoot]) {
                // 快门也要亮灯
                System.shut_led_countdown = SHUT_LED_TIME;
                System.shut_led_status = true;
                LED_RED_WriteH;
                rf_send_master_pilot();
            } else {
                if (Nikon.has_shutter_flag) {
                    // 快门也要亮灯
                    System.shut_led_countdown = SHUT_LED_TIME;
                    System.shut_led_status = true;
                    LED_RED_WriteH;
                    rf_send_0x5C_start();
                    if (Param.is_Multi_page) {
                        uint8_t check_multi_switch = 0;
                        for (int j = 0; j < GroupItemCount; ++j) {
                            check_multi_switch |= (Param.group[j].is_turn_on_Multi & 1) << j;
                        }
                        if (check_multi_switch) {
                            flash_multi_mode(shutter_event | rf_tx_event, Param.multi.flash_level,
                                             Param.multi.flash_times,
                                             Param.multi.flash_freq);
                        }
                    }
                    // else {
                    //     if (MasterFlag.bit.Mmode == TTL_Mode) {
                    //         if (NormalRam.FPShutter)
                    //             Flash_TTLFP_Mode_Prog(NormalRam.FlashFPLV);
                    //         else
                    //             Flash_TTLLP_Mode(NormalRam.FlashLPLV);
                    //     } else if (MasterFlag.bit.Mmode == MUL_Mode) {
                    //         if (NormalRam.FPShutter)
                    //             Flash_MULFP_Mode_Prog(MasterRam.MmulLV);
                    //         else
                    //             Flash_MULLP_Mode(MasterRam.MmulLV);
                    //     }
                    // }
                    rf_send_0x5C_end();
                    cc2500_set_mode(RF_stby_mode);
                    Nikon.has_shutter_flag = false;
                }
            }
        }
    } else if (GPIO_Pin == MAIN_SWITCH_Pin) {
        System.from_MAIN_SWITCH_INT = true;
        if (!System.switch_status) {
            System.switch_status = true;
        }
    } else if (GPIO_Pin == WKUP_EXTI0_Pin) {
        delay_ms(5);
        // 拔掉USB线就复位
        // 同时避开按键松手产生的WKUP下降沿
        // 以及上电后持续按住开关键,此时不会产生MAIN_SWITCH下降沿,即无法将from_MAIN_SWITCH_INT置1;
        if (!WKUP_EXTI0_Read) {
            // 在充电页拔掉线才关机
            if (!System.from_MAIN_SWITCH_INT && !System.MAIN_SWITCH_is_pressed && Param.is_charging_page) {
                device_deinit();
                // device_sw_reset();
            }
            BatteryCharge.usb_is_plugged = false;
            BatteryCharge.last_usb_is_plugged = false;
        }
    } else if (GPIO_Pin == CHG_OK_EXTI4_Pin) {
        delay_us(5);
        if (!CHG_OK_Read) {
            if (CHG_OK_Read != BatteryCharge.last_state) {
                BatteryCharge.is_level_change = true;
                BatteryCharge.last_state = CHG_OK_Read;
                BatteryCharge.level_change_count++; // 电平变化时增加计数
            }
        }
    } else if (GPIO_Pin == AMOLED_TE_Pin) {
        if (System.dma_transfer_complete) {
            // disp_my = lv_disp_get_default();
            // lv_disp_flush_ready(disp_my->driver);
            lv_disp_flush_ready(lv_disp_get_default()->driver);
        }
    }
    // Exti Clear again
    __HAL_GPIO_EXTI_CLEAR_FALLING_IT(GPIO_Pin);
}

void HAL_GPIO_EXTI_Rising_Callback(uint16_t GPIO_Pin) {
    if (GPIO_Pin == MAIN_SWITCH_Pin) {
        // delay_us(5);
        // if (MAIN_SWITCH_Read) {
        //     System.from_MAIN_SWITCH_INT = false;
        //     if (WKUP_EXTI0_Read) {
        //         BatteryCharge.usb_is_plugged = true;
        //         BatteryCharge.last_usb_is_plugged = true;
        //         // if (Param.is_charging_page) {
        //         //     send_update_charging_page();
        //         // }
        //     }
        // }
    } else if (GPIO_Pin == CHG_OK_EXTI4_Pin) {
        delay_us(5);
        if (CHG_OK_Read && WKUP_EXTI0_Read && MAIN_SWITCH_Read) {
            if (CHG_OK_Read != BatteryCharge.last_state) {
                BatteryCharge.is_level_change = true;
                BatteryCharge.last_state = CHG_OK_Read;
                // 电平变化时增加计数
                BatteryCharge.level_change_count++;
            }
        }
    } else if (GPIO_Pin == WKUP_EXTI0_Pin) {
        // delay_us(5);
        // if (WKUP_EXTI0_Read) {
        amoled_keep_alive();
        // }
    }
    __HAL_GPIO_EXTI_CLEAR_RISING_IT(GPIO_Pin);
}

// void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim) {
// if (htim == &htim7) {
// Test_1_WriteH;
// scan_key_pgr();
// Test_1_WriteL;
// }
// }
