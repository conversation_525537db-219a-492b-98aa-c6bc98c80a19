//
// Created by <PERSON><PERSON> on 2024/5/30.
//

#include "amoled_qspi.h"
#include "user.h"

// #define COL    368
// #define ROW    448

// extern XSPI_HandleTypeDef hospi1;

XSPI_RegularCmdTypeDef qspi_cmd;

typedef struct {
    uint8_t cmd;
    uint8_t data;
    uint8_t data_len;
} amoled_cmd_t;

amoled_cmd_t init_cmds[] = {
        {0xFE, 0x00, 1},
        {0x51, 0x1A, 1},
        {0x39, 0x00, 0},
        {0xFE, 0x20, 1},
        {0xF4, 0x5A, 1},
        {0xF5, 0x59, 1},
        {0xFE, 0x40, 1},
        {0x61, 0x02, 1},
        {0x62, 0x07, 1},
        {0xFE, 0x70, 1},
        {0x26, 0x04, 1},
        {0xFE, 0x20, 1},
        {0xF4, 0xA5, 1},
        {0xF5, 0xA5, 1},
        {0xFE, 0x20, 1},
        {0xF4, 0x5A, 1},
        {0xF5, 0x59, 1},
        {0xFE, 0x70, 1},
        {0x26, 0x38, 1},
        {0xFE, 0x20, 1},
        {0xF4, 0xA5, 1},
        {0xF5, 0xA5, 1},
        {0xFE, 0x00, 1},
        {0x38, 0x00, 0},
        {0x51, 0x1A, 1},
        // {0x3A, 0x55, 1},
        // {0xFE, 0x00, 1},
        // {0xC4, 0x80, 1},
        // {0x3A, 0x55, 1},
        // {0x35, 0x00, 1},
        // {0x53, 0x20, 1}
};

void amoled_write(uint8_t cmd, uint8_t *data, uint8_t data_num) {
    uint32_t command = (0x02) << 24 | cmd << 8;
    qspi_cmd.InstructionMode = HAL_XSPI_INSTRUCTION_1_LINE;
    qspi_cmd.Instruction = command;
    qspi_cmd.AddressMode = HAL_XSPI_ADDRESS_NONE;
    if (!data_num) {
        qspi_cmd.DataMode = HAL_XSPI_DATA_NONE;
        HAL_XSPI_Command(&hospi1, &qspi_cmd, HAL_XSPI_TIMEOUT_DEFAULT_VALUE);
        qspi_cmd.DataMode = HAL_XSPI_DATA_1_LINE;
        return;
    }
    qspi_cmd.DataMode = HAL_XSPI_DATA_1_LINE;
    qspi_cmd.DataLength = data_num;
    HAL_XSPI_Command(&hospi1, &qspi_cmd, HAL_XSPI_TIMEOUT_DEFAULT_VALUE);
    HAL_XSPI_Transmit(&hospi1, data, HAL_XSPI_TIMEOUT_DEFAULT_VALUE);
}

void amoled_write_batch(amoled_cmd_t *cmds, uint32_t count) {
    for (uint32_t i = 0; i < count; i++) {
        amoled_write(cmds[i].cmd, cmds[i].data_len ? &cmds[i].data : NULL, cmds[i].data_len);
        delay_us(40);
    }
}

void amoled_write_4l(uint8_t *data, uint32_t data_num) {
    qspi_cmd.InstructionMode = HAL_XSPI_INSTRUCTION_4_LINES;
    // 4线地址须传0x12，先转成2进制再+0x才生效
    qspi_cmd.Instruction = 0x00010010;
    // 1线地址须传0x32，先转成2进制再+0x才生效
    // qspi_cmd.Instruction = 0x00110010;
    // qspi_cmd.AddressMode = HAL_XSPI_ADDRESS_1_LINE;
    qspi_cmd.AddressMode = HAL_XSPI_ADDRESS_4_LINES;
    qspi_cmd.AddressWidth = HAL_XSPI_ADDRESS_24_BITS;
    qspi_cmd.AddressDTRMode = HAL_XSPI_ADDRESS_DTR_DISABLE;
    qspi_cmd.Address = 0x002C00;
    qspi_cmd.DataMode = HAL_XSPI_DATA_4_LINES;
    qspi_cmd.DataLength = data_num;
    if (!data_num) {
        qspi_cmd.DataMode = HAL_XSPI_DATA_NONE;
        HAL_XSPI_Command(&hospi1, &qspi_cmd, HAL_XSPI_TIMEOUT_DEFAULT_VALUE);
        qspi_cmd.DataMode = HAL_XSPI_DATA_4_LINES;
        return;
    }

    HAL_XSPI_Command(&hospi1, &qspi_cmd, HAL_XSPI_TIMEOUT_DEFAULT_VALUE);
    HAL_XSPI_Transmit_DMA(&hospi1, data);
}

void BlockWrite(unsigned int Xstart, unsigned int Xend, unsigned int Ystart, unsigned int Yend) {
    qspi_cmd.InstructionMode = HAL_XSPI_INSTRUCTION_1_LINE;
    qspi_cmd.AddressMode = HAL_XSPI_ADDRESS_NONE;
    Xstart += 0x10;
    Xend += 0x10;

    uint8_t data_2a_arr[] = {Xstart >> 8, Xstart & 0xFF, Xend >> 8, Xend & 0xFF};
    amoled_write(0x2A, data_2a_arr, 4);

    uint8_t data_2b_arr[] = {Ystart >> 8, Ystart & 0xFF, Yend >> 8, Yend & 0xFF};
    amoled_write(0x2B, data_2b_arr, 4);
}

// 定义DMA传输完成标志
// volatile bool dma_transfer_complete = false;

// DMA传输完成回调
void HAL_XSPI_TxCpltCallback(XSPI_HandleTypeDef *hxspi) {
    if (hxspi == &hospi1) {
        System.dma_transfer_complete = true;
    }
}

#define DMA_TIMEOUT 100

void amoled_fill_color(uint16_t xsta, uint16_t ysta, uint16_t xend, uint16_t yend, uint16_t *color) {
    // 计算DMA最大传输大小（根据实际硬件配置调整）
    const uint16_t dma_max_row = 70;
    const uint32_t dma_block_size = (xend - xsta + 1) * dma_max_row * 2;
    const uint32_t total_size = (xend - xsta + 1) * (yend - ysta + 1) * 2;
    uint32_t surplus_size = total_size;
    uint32_t timeout;

    while (surplus_size > 0) {
        System.dma_transfer_complete = false;

        if (surplus_size >= dma_block_size) {
            BlockWrite(xsta, xend, ysta, ysta + dma_max_row - 1);
            amoled_write_4l((uint8_t *) color, dma_block_size);

            // 等待DMA传输完成，带超时
            timeout = HAL_GetTick();
            while (!System.dma_transfer_complete) {
                // 100ms超时
                if (HAL_GetTick() - timeout > DMA_TIMEOUT) {
                    // 处理超时错误
                    return;
                }
            }

            color += dma_block_size / 2;
            surplus_size -= dma_block_size;
            ysta += dma_max_row;
        } else {
            BlockWrite(xsta, xend, ysta, yend);
            amoled_write_4l((uint8_t *) color, surplus_size);

            // 等待DMA传输完成，带超时
            timeout = HAL_GetTick();
            while (!System.dma_transfer_complete) {
                // 100ms超时
                if (HAL_GetTick() - timeout > DMA_TIMEOUT) {
                    // 处理超时错误
                    return;
                }
            }

            surplus_size = 0;
        }
    }
}

// void amoled_fill_color(uint16_t xsta, uint16_t ysta, uint16_t xend, uint16_t yend, uint16_t *color) {
//     // 70*410*2 = 57400 DMA最大是65536
//     uint16_t dma_max_row = 70;
//     uint32_t dma_block_size = (xend - xsta + 1) * dma_max_row * 2;
//     uint32_t total_size = (xend - xsta + 1) * (yend - ysta + 1) * 2;
//     uint32_t surplus_size = total_size;
//
//     while (surplus_size > 0) {
//         if (surplus_size >= dma_block_size) {
//             BlockWrite(xsta, xend, ysta, ysta + dma_max_row - 1);
//             amoled_write_4l((uint8_t *) color, dma_block_size);
//             color += dma_block_size / 2;
//             surplus_size -= dma_block_size;
//             ysta += dma_max_row;
//             while (hospi1.State != HAL_XSPI_STATE_READY);
//         } else {
//             BlockWrite(xsta, xend, ysta, yend);
//             amoled_write_4l((uint8_t *) color, surplus_size);
//             while (hospi1.State != HAL_XSPI_STATE_READY);
//             surplus_size = 0;
//         }
//     }
// }

void amoled_set_brightness_code(uint8_t data) {
    amoled_write(0x51, &data, 1);
}

// 传0-100
void amoled_set_brightness_level(uint8_t level) {
    const uint8_t min_val = 0x38;  // 26
    const uint8_t max_val = 0xFF;  // 255
    uint8_t data = min_val + level * ((max_val - min_val) / MAX_BRIGHTNESS_LEVEL);
    amoled_set_brightness_code(data);
    System.last_brightness_level = level;
}

void amoled_keep_alive() {
    if (System.last_brightness_level == 0xFF) {
        SystemClock_Config_250M();
    } else{
        if (System.is_sys_clock_50M) {
            SystemClock_Config_250M();
        }
    }

    if (Param.is_charging_page) {
        if (Param.battery_status == battery_charging) {
            // 待机时间改为30s
            System.standby_countdown = CHG_ING_STANDBY_TIME;
        } else {
            System.standby_countdown = CHG_OK_STANDBY_TIME;
        }
        System.power_off_countdown = 0xFF;
    } else {
        System.standby_countdown = standby_time[Setting.roller_values[roller_setting_standby]];
        System.power_off_countdown = power_off_time[Setting.roller_values[roller_setting_off]];
    }

    if (System.last_brightness_level != Param.brightness.level || System.last_brightness_level == 0xFF) {
        if (Param.is_charging_page) {
            amoled_set_brightness_level(CHG_PAGE_AMOLED_LEVEL);
        } else {
            amoled_set_brightness_level(Param.brightness.level);
        }
        uint8_t data = 0x00;
        amoled_write(0xFE, &data, 1);
        delay_us(40);
        amoled_write(0x38, 0x00, 0);
        // delay_us(40);
        // amoled_write(0x48, 0x00, 0);
    }
    HAL_LPTIM_TimeOut_Stop_IT(&hlptim2);
}

void amoled_enter_sleep() {
    amoled_write(0xFE, 0, 0);
    amoled_write(0xEF, 0, 0);
    // 关显示
    amoled_write(0x28, 0, 0);
    delay_us(200);
    // 进休眠
    amoled_write(0x10, 0, 0);
    System.amoled_is_sleep = true;
    SystemClock_Config_50M();
}

void amoled_exit_sleep() {
    amoled_write(0xFE, 0, 0);
    amoled_write(0xEF, 0, 0);
    // 退休眠
    amoled_write(0x11, 0, 0);
    delay_us(200);
    // 开显示
    amoled_write(0x29, 0, 0);
    System.amoled_is_sleep = false;
    SystemClock_Config_250M();
}

// void DispColor(unsigned int color) {
//     // unsigned int i, j;
//
//     // BlockWrite(0, COL - 1, 0, ROW - 1);
//     //
//     // uint8_t data_2b_arr[] = {0x00, 0x00, 0x01, 0xF5};
//     //
//     // // for (int k = 0; k < COL * ROW; ++k) {
//     // //     arr[k] = 0xFF;
//     // // }
//     // static
//     uint8_t color_data[Screen_Width * Screen_Height * 2];
//     for (int i = 0; i < Screen_Width * Screen_Height * 2;) {
//         color_data[i] = color & 0xFF;
//         color_data[i + 1] = color >> 8;
//         i += 2;
//     }
//
//     __NOP();
//     BlockWrite(0, Screen_Width - 1, 0, Screen_Height - 1);
//
//     amoled_write_4l(color_data, Screen_Width * Screen_Height * 2);
//
//     // for (int i = 0; i < 410 * 502 * 2;) {
//     //     color_data[i] = 0x00;
//     //     color_data[i + 1] = 0x00;
//     //     i += 2;
//     // }
//     //
//     // BlockWrite(10, 20, 10, 20);
//     //
//     // amoled_write_4l(color_data, 100);
//
//
//     // for (i = 0; i < COL; i++) {
//     //     for (j = 0; j < ROW; j++) {
//     //         SPI_WriteData1(color >> 8);
//     //         SPI_WriteData1(color);
//     //     }
//     // }
// }

void qspi_cmd_init() {
    uint8_t data = 0;
    qspi_cmd.InstructionWidth = HAL_XSPI_INSTRUCTION_32_BITS;
    qspi_cmd.InstructionMode = HAL_XSPI_INSTRUCTION_1_LINE;
    qspi_cmd.InstructionDTRMode = HAL_XSPI_INSTRUCTION_DTR_DISABLE;
    qspi_cmd.AddressMode = HAL_XSPI_ADDRESS_NONE;

    qspi_cmd.AlternateBytes = HAL_XSPI_ALT_BYTES_NONE;
    qspi_cmd.DataDTRMode = HAL_XSPI_DATA_DTR_DISABLE;
    qspi_cmd.DataMode = HAL_XSPI_DATA_1_LINE;

    qspi_cmd.DataLength = 3;
    qspi_cmd.OperationType = HAL_XSPI_OPTYPE_COMMON_CFG;
    qspi_cmd.DQSMode = HAL_XSPI_DQS_DISABLE;
    qspi_cmd.DummyCycles = 0;
    qspi_cmd.SIOOMode = HAL_XSPI_SIOO_INST_ONLY_FIRST_CMD;
}

// uint16_t color_arr[Screen_Width * 70 * 2];

void amoled_init() {
    qspi_cmd_init();

    AMOLED_RST_WriteH;
    delay_ms(20);
    AMOLED_RST_WriteL;
    delay_ms(80);
    AMOLED_RST_WriteH;
    delay_ms(80);

    uint8_t data = 0x00;
    // amoled_write(0xFE, &data, 1);
    // data = 0x00;
    // amoled_write(0x39, &data, 0);
    //
    // data = 0x20;
    // amoled_write(0xFE, &data, 1);
    // data = 0x5A;
    // amoled_write(0xF4, &data, 1);
    // data = 0x59;
    // amoled_write(0xF5, &data, 1);
    //
    // data = 0x40;
    // amoled_write(0xFE, &data, 1);
    // data = 0x02;
    // amoled_write(0x61, &data, 1);
    // data = 0x07;
    // amoled_write(0x62, &data, 1);
    //
    // data = 0x70;
    // amoled_write(0xFE, &data, 1);
    // data = 0x04;
    // amoled_write(0x26, &data, 1);
    //
    // data = 0x20;
    // amoled_write(0xFE, &data, 1);
    // data = 0xA5;
    // amoled_write(0xF4, &data, 1);
    // data = 0xA5;
    // amoled_write(0xF5, &data, 1);
    //
    // data = 0x20;
    // amoled_write(0xFE, &data, 1);
    // data = 0x5A;
    // amoled_write(0xF4, &data, 1);
    // data = 0x59;
    // amoled_write(0xF5, &data, 1);
    //
    // data = 0x70;
    // amoled_write(0xFE, &data, 1);
    // data = 0xC0;
    // amoled_write(0x26, &data, 1);
    //
    // data = 0x20;
    // amoled_write(0xFE, &data, 1);
    // data = 0xA5;
    // amoled_write(0xF4, &data, 1);
    // data = 0xA5;
    // amoled_write(0xF5, &data, 1);
    //
    // data = 0x00;
    // amoled_write(0xFE, &data, 1);
    // data = 0x38;
    // amoled_write(0xF4, &data, 0);
    amoled_write_batch(init_cmds, sizeof(init_cmds) / sizeof(init_cmds[0]));

    data = 0x55;
    amoled_write(0x3A, &data, 1);

    data = 0x00;
    amoled_write(0xFE, &data, 1);

    data = 0x80;
    amoled_write(0xC4, &data, 1);

    data = 0x55;
    amoled_write(0x3A, &data, 1);

    data = 0x00;
    amoled_write(0x35, &data, 1);

    data = 0x20;
    amoled_write(0x53, &data, 1);


    if (Param.brightness.level) {
        amoled_set_brightness_level(Param.brightness.level);
    } else {
        data = 0xFF;
        amoled_write(0x51, &data, 1);
    }

    data = 0xFF;
    amoled_write(0x63, &data, 1);

    // data = 0xC0;
    // amoled_write(0x36, &data, 1);

    uint8_t data_44_arr[] = {0x00, 0x90};
    amoled_write(0x44, data_44_arr, 2);

    uint8_t data_2a_arr[] = {0x00, 0x10, 0x01, 0x7F};
    amoled_write(0x2A, data_2a_arr, 4);

    uint8_t data_2b_arr[] = {0x00, 0x00, 0x01, 0xBF};
    amoled_write(0x2B, data_2b_arr, 4);

    // SPI_CS=0;SPI_WriteComm(0x2A);SPI_WriteData(0x00);SPI_WriteData(0x10);SPI_WriteData(0x01);SPI_WriteData(0x7F);SPI_CS=1;
    // SPI_CS=0;SPI_WriteComm(0x2B);SPI_WriteData(0x00);SPI_WriteData(0x00);SPI_WriteData(0x01);SPI_WriteData(0xBF);SPI_CS=1;

    amoled_write(0x11, &data, 0);

    amoled_write(0x29, &data, 0);

    // color_data作为局部数组可能会因为堆栈不够导致异常
    // uint8_t color_data[Screen_Width * Screen_Height * 2];

    // DispColor(0x0000);
    // uint16_t color = 0x64BD;
    // amoled_set_full_screen_black();
    // data = 0x20;
    // amoled_write(0x53, &data, 1);
    // data = 0x60;
    // amoled_write(0x51, &data, 1);
    // while(1);
    // amoled_fill_color(0, 0, Screen_Width, 69, color_arr);
    // amoled_fill_color(0, 70, Screen_Width, 139, color_arr);
    // amoled_fill_color(0, 140, Screen_Width, 209, color_arr);
    // amoled_fill_color(0, 210, Screen_Width, 209, color_arr);


    // amoled_write(0xfe, &data, 0);
    // amoled_write(0xef, &data, 0);
    // amoled_write(0x28, &data, 0);
    // HAL_Delay(200);
    // amoled_write(0x10, &data, 0);

    // SPI_WriteComm(0xfe);
    // SPI_WriteComm(0xef);
    // SPI_WriteComm(0x28);
    // Delay(200);
    // SPI_WriteComm(0x10);

    // for (int i = 0; i < Screen_Width * 70 * 2;) {
    //     color_arr[i] = 0xFF;
    //     color_arr[i + 1] = 0x00;
    //     i += 2;
    // }
    // amoled_fill_color(0, 0 * 70, Screen_Width, 70 * (0 + 1) - 1, color_arr);
}