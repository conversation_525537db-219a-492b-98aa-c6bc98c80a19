//
// Created by <PERSON><PERSON> on 2024/10/18.
//

#include <string.h>
#include "adc_dma.h"
#include "user.h"
#include "view_model.h"
#include "system.h"
#include "amoled_qspi.h"

ADCStruct ADC_DMA;
BatteryChargeStruct BatteryCharge;
BatteryChargeStruct BatteryCharge;

void adc_clear_buffer() {
    ADC_DMA.DMA_count = 0;
    // for (int i = 0; i < ADC_CH_Count; i++) {
    //     // ADC_DMA.DMA_buffer[i] = 0;
    //     ADC_DMA.DMA_data_accumulated[i] = 0;
    // }
    memset(ADC_DMA.DMA_buffer, 0, sizeof(ADC_DMA.DMA_buffer));
    memset(ADC_DMA.DMA_data_accumulated, 0, sizeof(ADC_DMA.DMA_data_accumulated));
}

void adc_start() {
    adc_clear_buffer();

    /* Perform ADC calibration */
    if (HAL_ADCEx_Calibration_Start(&hadc1, ADC_SINGLE_ENDED) != HAL_OK) {
        /* Calibration Error */
        Error_Handler();
    }

    /* Start ADC group regular conversion */
    if (HAL_ADC_Start_DMA(&hadc1, (uint32_t *) ADC_DMA.DMA_buffer, (uint32_t) ADC_CH_Count) != HAL_OK) {
        Error_Handler();
    }
}

void get_vrefint_cal() {
    // 读系统宏定义需先关闭ICACHE
    if (HAL_ICACHE_Disable() != HAL_OK) {
        Error_Handler();
    }
    delay_us(100);
    // ADC_DMA.vrefint_cal = *(uint16_t *) (0x08FFF810);
    // 1514 1512
    ADC_DMA.vrefint_cal = *VREFINT_CAL_ADDR;
    if (ADC_DMA.vrefint_cal < 1300 || ADC_DMA.vrefint_cal > 1800) {
        ADC_DMA.vrefint_cal = 1512;
    }
    if (HAL_ICACHE_Enable() != HAL_OK) {
        Error_Handler();
    }
}

void get_adc() {
    // 在采样值数组中分别取出每个通道的采样值并求和
    if (ADC_DMA.DMA_count < (1 << 3)) {
        ADC_DMA.DMA_count++;
        for (uint8_t i = 0; i < ADC_CH_Count; i++) {
            ADC_DMA.DMA_data_accumulated[i] += ADC_DMA.DMA_buffer[i];
        }
    } else {
        // 右移3位即除以8
        // 移位的运算速度比除法更快
        for (uint8_t i = 0; i < ADC_CH_Count; ++i) {
            ADC_DMA.DMA_aver_buffer[i] = ADC_DMA.DMA_data_accumulated[i] >> 3;
        }
        adc_clear_buffer();
    }
}

void adc_cal() {
    // 0 vol BAT
    // 1 vol mcu
    // 2 temp mcu

    get_adc();

    // ADC_DMA.vol_mcu = 1200 * 4095 / ADC_DMA.DMA_aver_buffer[1];

    // 使用 (x + y/2) / y 的方式来实现四舍五入
    // VREFINT_CAL_VREF 3300
    ADC_DMA.vol_mcu = (3300 * ADC_DMA.vrefint_cal + ADC_DMA.DMA_aver_buffer[1] / 2) / ADC_DMA.DMA_aver_buffer[1];
    uint16_t mcu_get_bat_vol = (ADC_DMA.DMA_aver_buffer[0] * ADC_DMA.vol_mcu + 4095 / 2) / 4095;

    // ADC_DMA.vol_bat = (mcu_get_bat_vol * (300 + 100) + 100 / 2) / 100;
#if STATIC_BAT
    if (!ADC_DMA.vol_bat) {
        ADC_DMA.vol_bat = STATIC_BAT_VOL;
    }
#else
    ADC_DMA.vol_bat = (mcu_get_bat_vol * (300 + 100) + 100 / 2) / 100;
#endif
    // double VSense = (double) ADC_DMA.DMA_aver_buffer[2] * ADC_DMA.vol_mcu / 4096;
    // ADC_DMA.temp_mcu = (uint16_t) ((VSense - 1430) / 4.3 + 25);
}


// 新增滤波和防跳动的电池电量检测
void battery_level_detection() {
    static uint16_t voltage_history[8] = {0}; // 电压历史记录
    static uint8_t history_index = 0;
    static uint8_t level_stable_count[4] = {0}; // 各电量等级稳定计数
    static uint8_t last_detected_level = 0xFF;

    // 滑动平均滤波
    voltage_history[history_index] = ADC_DMA.vol_bat;
    history_index = (history_index + 1) % 8;

    // 计算平均电压
    uint32_t voltage_sum = 0;
    for (uint8_t i = 0; i < 8; i++) {
        voltage_sum += voltage_history[i];
    }
    uint16_t filtered_voltage = voltage_sum >> 3; // 除以8

    // 根据充电状态调整阈值，避免临界值跳动
    uint16_t level_thresholds[4];
    if (BatteryCharge.usb_is_plugged) {
        // 充电时使用较低阈值，便于电量上升
        level_thresholds[0] = BAT_LEVEL_OFF;
        level_thresholds[1] = BAT_LEVEL_1 - 50;
        level_thresholds[2] = BAT_LEVEL_2 - 50;
        level_thresholds[3] = BAT_LEVEL_3 - 50;
    } else {
        // 放电时使用较高阈值，防止电量虚高
        level_thresholds[0] = BAT_LEVEL_OFF;
        level_thresholds[1] = BAT_LEVEL_1 + 50;
        level_thresholds[2] = BAT_LEVEL_2 + 50;
        level_thresholds[3] = BAT_LEVEL_3 + 50;
    }

    // 确定当前电量等级
    uint8_t current_level = 0;
    if (filtered_voltage >= level_thresholds[3]) {
        current_level = 3;
    } else if (filtered_voltage >= level_thresholds[2]) {
        current_level = 2;
    } else if (filtered_voltage >= level_thresholds[1]) {
        current_level = 1;
    } else if (filtered_voltage >= level_thresholds[0]) {
        current_level = 0;
    } else {
        // 电压过低，准备关机
        System.is_mine_shutdown = true;
        Param.battery_status = battery_off;
        return;
    }

    // 防跳动逻辑：需要连续检测到同一等级15次才更新
    if (current_level == last_detected_level) {
        level_stable_count[current_level]++;
        // 清零其他等级计数
        for (uint8_t i = 0; i < 4; i++) {
            if (i != current_level) {
                level_stable_count[i] = 0;
            }
        }
    } else {
        // 检测到不同等级，重置计数
        memset(level_stable_count, 0, sizeof(level_stable_count));
        last_detected_level = current_level;
        level_stable_count[current_level] = 1;
    }

    // 稳定计数达到阈值才更新电量
    if (level_stable_count[current_level] >= 15) {
        if (BatteryCharge.usb_is_plugged) {
            // 充电时电量只能增加
            if (current_level > Param.electricity_level) {
                Param.electricity_level = current_level;
                ADC_DMA.electricity_level_max = current_level;
            }
        } else {
            // 放电时电量只能减少
            if (ADC_DMA.electricity_level_max == 0) {
                ADC_DMA.electricity_level_max = current_level;
                Param.electricity_level = current_level;
            } else if (current_level < Param.electricity_level) {
                Param.electricity_level = current_level;
                ADC_DMA.electricity_level_max = current_level;
            }
        }

        // 重置计数器
        memset(level_stable_count, 0, sizeof(level_stable_count));
    }
}

void battery_chg_ok_init() {
    BatteryCharge.is_level_change = false;
    // BatteryCharge.check_time = 0;
    BatteryCharge.high_time = 0;
    BatteryCharge.low_time = 0;
    BatteryCharge.high_level_start_time = 0;
    // 电平变化计数器
    BatteryCharge.level_change_count = 0;
}

// 充电是L电平，涓流是PWM，满电是H电平
void battery_CHG_OK_check() {
#if !STATIC_BAT
    // USB插入条件
    bool is_usb_in = WKUP_EXTI0_Read && MAIN_SWITCH_Read;

    if (is_usb_in) {
        BatteryCharge.usb_is_plugged = true;
    } else {
        // 若上一次已记录usb插入，此时WKUP拉高，开关拉低，仍视为usb插入状态
        if (BatteryCharge.last_usb_is_plugged && !MAIN_SWITCH_Read && WKUP_EXTI0_Read) {
            BatteryCharge.usb_is_plugged = true;
        } else {
            BatteryCharge.usb_is_plugged = false;
            ADC_DMA.vol_full_count = 0;
        }
    }

    // 记录当前usb插入状态
    BatteryCharge.last_usb_is_plugged = BatteryCharge.usb_is_plugged;

    if (BatteryCharge.usb_is_plugged && Param.battery_status == battery_complete) {
        return;
    }

    if (BatteryCharge.usb_is_plugged) {
        if (ADC_DMA.vol_bat >= BAT_LEVEL_FUll + 30) {
            ADC_DMA.vol_full_count++;
            if (ADC_DMA.vol_full_count > 1000) {
                Param.battery_status = battery_complete;
                battery_chg_ok_init();
                return;
            }
        } else {
            ADC_DMA.vol_full_count = 0;
        }
    }
#endif
}

#if 0
// 充电是L电平，涓流是PWM，满电是H电平
void battery_CHG_OK_check() {


#if !STATIC_BAT
// USB插入条件
bool is_usb_in = WKUP_EXTI0_Read && MAIN_SWITCH_Read;

    if (is_usb_in){
        BatteryCharge.usb_is_plugged = true;
}else {
        // 若上一次已记录usb插入，此时WKUP拉高，开关拉低，仍视为usb插入状态
        if (BatteryCharge.last_usb_is_plugged && !MAIN_SWITCH_Read && WKUP_EXTI0_Read) {
            BatteryCharge.usb_is_plugged = true;
        } else {
            BatteryCharge.usb_is_plugged = false;
            ADC_DMA.vol_full_count = 0;
        }
    }

// 记录当前usb插入状态
BatteryCharge.last_usb_is_plugged = BatteryCharge.usb_is_plugged;

    if (BatteryCharge.usb_is_plugged&& Param.battery_status== battery_complete) {
        return;
    }

    if (BatteryCharge.usb_is_plugged) {
        BatteryCharge.check_time++;
        // 检测电平变化次数达到10次以上视为涓流充电
        if (BatteryCharge.level_change_count >= 10) {
            // 如果检测到涓流充电，则认为电池充满
            Param.battery_status = battery_complete;
            battery_chg_ok_init();
            return;
        }

        if (ADC_DMA.vol_bat >= BAT_LEVEL_FUll + 30) {
            ADC_DMA.vol_full_count++;
            if (ADC_DMA.vol_full_count > 50) {
                Param.battery_status = battery_complete;
                battery_chg_ok_init();
                return;
            }
        } else {
            ADC_DMA.vol_full_count = 0;
        }

        if (CHG_OK_Read) {
            BatteryCharge.high_time++;
            BatteryCharge.high_level_start_time++;
            // 如果当前是高电平，计算持续时间
            if (BatteryCharge.high_level_start_time >= 200) {
                // CHG_OK持续高电平超过200ms，视为充满
                Param.battery_status = battery_complete;
                battery_chg_ok_init();
                return;
            }
        } else {
            BatteryCharge.low_time++;
            BatteryCharge.high_level_start_time = 0;
        }

#if 0
// 检测电压大于4100mV作为充满条件之一
if (ADC_DMA.vol_bat>= 4150) {
            Param.battery_status = battery_complete;
            return;
        }
#endif

// 超过255ms没有检测到电平变化，重置计数
if (BatteryCharge.high_time>= 255 || BatteryCharge.low_time>= 255) {
            battery_chg_ok_init();
        }
    }
#endif
}
#endif

// 电池充电检测
void battery_charge_check() {
    // USB插入条件检测
    bool is_usb_in = WKUP_EXTI0_Read && MAIN_SWITCH_Read;

    if (is_usb_in) {
        BatteryCharge.usb_is_plugged = true;
    } else {
        if (BatteryCharge.last_usb_is_plugged && !MAIN_SWITCH_Read && WKUP_EXTI0_Read) {
            BatteryCharge.usb_is_plugged = true;
        } else {
            BatteryCharge.usb_is_plugged = false;
        }
    }

    // 充电状态变化处理
    if (BatteryCharge.usb_is_plugged != BatteryCharge.last_usb_is_plugged) {
        BatteryCharge.last_usb_is_plugged = BatteryCharge.usb_is_plugged;
    }

    // 设置电池状态
    if (BatteryCharge.usb_is_plugged) {
        if (Param.battery_status != battery_complete) {
            Param.battery_status = battery_charging;
        }
        if (Param.electricity_level == 0xFF) {
            Param.electricity_level = 0;
        }
    } else {
        if (Param.battery_status < battery_low) {
            if (Param.electricity_level > 0) {
                Param.battery_status = battery_normal;
            } else {
                Param.battery_status = battery_low;
            }
        }
    }

    // 使用新的电量检测函数
    battery_level_detection();

    // 同步参数
    if (Param.last_battery_status != Param.battery_status ||
        Param.last_electricity_level != Param.electricity_level) {
        sync_param_battery();
        Param.last_battery_status = Param.battery_status;
        Param.last_electricity_level = Param.electricity_level;
    }
}
