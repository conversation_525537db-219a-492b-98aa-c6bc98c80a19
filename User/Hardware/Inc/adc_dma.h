//
// Created by <PERSON><PERSON> on 2024/10/18.
//

#ifndef QZ_N_ADC_DMA_H
#define QZ_N_ADC_DMA_H

#include <stdbool.h>
#include "main.h"

#define ADC_CH_Count 3

#define ADC_CHANNEL_FRE 10

#define BAT_LEVEL_LOW 3300
#define BAT_LEVEL_1 3500
#define BAT_LEVEL_2 3700
#define BAT_LEVEL_3 3950
#define BAT_LEVEL_FUll 4150
// 插电电压差值
#define BAT_DIFF 110

typedef struct {
    uint16_t vol_bat;
    uint8_t vol_bat_h;
    uint8_t vol_bat_l;
    uint16_t vol_mcu;
    uint16_t vrefint_cal;

    uint16_t DMA_count;
    uint16_t DMA_buffer[ADC_CH_Count];
    uint16_t DMA_aver_buffer[ADC_CH_Count];
    uint32_t DMA_data_accumulated[ADC_CH_Count];

    uint8_t electricity_level_max;
    uint16_t vol_full_count;

    // 电池电量检测相关变量
    uint16_t voltage_history[8];        // 电压历史记录
    uint8_t history_index;              // 历史记录索引
    uint8_t level_stable_count[4];      // 各电量等级稳定计数
    uint8_t last_detected_level;        // 上次检测到的电量等级
    uint8_t low_voltage_count;          // 低电压计数器，用于防跳动
} ADCStruct;
extern ADCStruct ADC_DMA;

// 充电检测状态结构体
typedef struct {
    // uint8_t check_time;
    uint8_t high_level_start_time; // 持续高电平时间
    uint8_t high_time;            // 高电平时间
    uint8_t low_time;             // 低电平时间
    bool last_state;            // 上次电平状态
    bool is_trickle_charge;        // 是否为涓流充电
    bool is_level_change;        // 是否电平改变
    uint8_t level_change_count;    // 电平变化次数计数

    uint8_t volt_level_count[4];   // 记录各电量等级的计数
    bool usb_is_plugged;        // 记录上一次的充电状态
    bool last_usb_is_plugged;        // 记录上一次的充电状态
} BatteryChargeStruct;
extern BatteryChargeStruct BatteryCharge;

// 电池充电检测结构体
// typedef struct {
//    
// } BatteryChargeStruct;
// extern BatteryChargeStruct BatteryCharge;

// typedef struct {
//     uint16_t vol_bat;
//     uint16_t vol_mcu;
//     uint16_t temp_mcu;
// } SensorsStruct;
// extern SensorsStruct Sensors;

void adc_start();

void get_vrefint_cal();

void get_adc();

void adc_cal();

void battery_charge_check();

void battery_CHG_OK_check();

#endif //QZ_N_ADC_DMA_H
