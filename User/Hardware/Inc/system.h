//
// Created by <PERSON><PERSON> on 2024/5/30.
//

#ifndef QZ_N_SYSTEM_H
#define QZ_N_SYSTEM_H

#include <stdbool.h>
#include "main.h"
#include "stm32h5xx_ll_gpio.h"
#include "src/core/lv_group.h"

#define STATIC_BAT 0
#define STATIC_BAT_VOL 3850

// 打开中断
#define Enable_Interrupts __set_PRIMASK(0)
// 关闭中断
#define Disable_Interrupts __set_PRIMASK(1)

#define Test_1_WriteH HAL_GPIO_WritePin(TEST_1_GPIO_Port, TEST_1_Pin, GPIO_PIN_SET)
#define Test_1_WriteL HAL_GPIO_WritePin(TEST_1_GPIO_Port, TEST_1_Pin, GPIO_PIN_RESET)

#define LED_RED_WriteH LL_GPIO_SetOutputPin(LED_RED_GPIO_Port, LED_RED_Pin)
#define LED_RED_WriteL LL_GPIO_ResetOutputPin(LED_RED_GPIO_Port, LED_RED_Pin)
// #define LED_RED_WriteH HAL_GPIO_WritePin(LED_RED_GPIO_Port, LED_RED_Pin, GPIO_PIN_SET)
// #define LED_RED_WriteL HAL_GPIO_WritePin(LED_RED_GPIO_Port, LED_RED_Pin, GPIO_PIN_RESET)
#define LED_RED_WriteT HAL_GPIO_TogglePin(LED_RED_GPIO_Port, LED_RED_Pin)

#define MAIN_SWITCH_Read HAL_GPIO_ReadPin(MAIN_SWITCH_GPIO_Port, MAIN_SWITCH_Pin)

// 3.3电路开关（屏幕、蓝牙、RF）
#define BOOST_EN_WriteH HAL_GPIO_WritePin(BOOST_EN_GPIO_Port, BOOST_EN_Pin, GPIO_PIN_SET)
#define BOOST_EN_WriteL HAL_GPIO_WritePin(BOOST_EN_GPIO_Port, BOOST_EN_Pin, GPIO_PIN_RESET)

#define CAM_COMM_WriteH HAL_GPIO_WritePin(CAM_COMM_GPIO_Port, CAM_COMM_Pin, GPIO_PIN_SET)
#define CAM_COMM_WriteL HAL_GPIO_WritePin(CAM_COMM_GPIO_Port, CAM_COMM_Pin, GPIO_PIN_RESET)

#define BT_EN_WriteH HAL_GPIO_WritePin(BT_EN_GPIO_Port, BT_EN_Pin, GPIO_PIN_SET)
#define BT_EN_WriteL HAL_GPIO_WritePin(BT_EN_GPIO_Port, BT_EN_Pin, GPIO_PIN_RESET)

#define FLASH_TRI_Read HAL_GPIO_ReadPin(FLASH_TRI_GPIO_Port, FLASH_TRI_Pin)

#define WKUP_EXTI0_Read HAL_GPIO_ReadPin(WKUP_EXTI0_GPIO_Port, WKUP_EXTI0_Pin)

#define CHG_OK_Read HAL_GPIO_ReadPin(CHG_OK_EXTI4_GPIO_Port, CHG_OK_EXTI4_Pin)

#define SCREEN_NO_ACTION_TIME 15
#define ENCODER_NO_ACTION_TIME 8
#define SHUT_LED_TIME 80

#define CHG_PAGE_AMOLED_LEVEL   30
#define CHG_OK_STANDBY_TIME   15
#define CHG_ING_STANDBY_TIME   3

// 时间轮询结构体
typedef struct {
    uint8_t t1ms;
    uint8_t t10ms;
    uint8_t t50ms;
    uint8_t t100ms;
    uint8_t t500ms;
    uint8_t t1000ms;
} TimePollInterval;

typedef enum {
    res_to_default,
    res_to_charging_page,
    res_to_main_page,
    res_to_main_page_no_welcome,
    res_to_MAX = res_to_main_page_no_welcome
} ResPageEnum;

typedef struct {
    // 降低亮度计数
    uint16_t standby_countdown;
    // 关机计数
    uint16_t power_off_countdown;
    // 失去焦点计数
    uint8_t defocused_countdown;
    // 上一次的亮度值
    volatile uint8_t last_brightness_level;
    // power_key长按计数
    // uint32_t power_key_press_count;
    // uint32_t power_key_press_count1;
    // 设备开关状态
    bool switch_status;
    // led亮起时间计数
    uint16_t shut_led_countdown;
    // 快门触发红灯亮
    bool shut_led_status;

    // 从按键进的中断，避免开机后持续按住开关键导致的关机
    bool from_MAIN_SWITCH_INT;
    // 记录开关下降沿时长100ms为单位
    uint8_t MAIN_SWITCH_low_level_counter;
    // 记录进入主程序后MAIN_SWITCH仍被按下
    bool MAIN_SWITCH_is_pressed;
    // 不喂狗标志
    bool do_not_refresh_WWDG;
    // 屏幕休眠标志
    bool amoled_is_sleep;

    uint8_t off_count;
    bool is_from_lptim;
    bool is_off;
    bool is_standby;
    bool is_sys_clock_50M;

    // 定义DMA传输完成标志
    // 必须volatile
    volatile bool dma_transfer_complete;

    uint8_t save_data_countdown;

    // 目前仅控制插电关机，设为uint8_t保留后续改为枚举的可能
    ResPageEnum res_to;

    lv_group_t *indev_group;
    bool nikon_sda_is_falling;
    bool is_connecting;
    // bool
} SystemStructType;
extern SystemStructType System;

extern uint16_t standby_time[];
extern uint16_t power_off_time[];

void user_pgr();

void delay_us(uint32_t count);

void delay_ms(uint16_t count);

void device_deinit();

void device_sw_reset();

void data_reset();

void standby_pgr();

void SystemClock_Config_50M();

void SystemClock_Config_250M();

void SystemClock_Config_8M();

#endif //QZ_N_SYSTEM_H
