/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.h
  * @brief          : Header for main.c file.
  *                   This file contains the common defines of the application.
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2024 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __MAIN_H
#define __MAIN_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32h5xx_hal.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "my_main.h"
/* USER CODE END Includes */

/* Exported types ------------------------------------------------------------*/
/* USER CODE BEGIN ET */
extern I2C_HandleTypeDef hi2c1;
extern ADC_HandleTypeDef hadc1;
extern SPI_HandleTypeDef hspi1;
extern XSPI_HandleTypeDef hospi1;
extern TIM_HandleTypeDef htim6;
extern LPTIM_HandleTypeDef hlptim1;
extern LPTIM_HandleTypeDef hlptim2;
// extern LPTIM_HandleTypeDef hlptim3;
extern UART_HandleTypeDef huart3;
extern DMA_HandleTypeDef handle_GPDMA1_Channel0;
extern DMA_HandleTypeDef handle_GPDMA1_Channel1;
extern DCACHE_HandleTypeDef hdcache1;
/* USER CODE END ET */

/* Exported constants --------------------------------------------------------*/
/* USER CODE BEGIN EC */

/* USER CODE END EC */

/* Exported macro ------------------------------------------------------------*/
/* USER CODE BEGIN EM */

/* USER CODE END EM */

/* Exported functions prototypes ---------------------------------------------*/
void Error_Handler(void);
void MX_GPIO_Init(void);
void MX_GPDMA1_Init(void);
void MX_OCTOSPI1_Init(void);
void MX_ICACHE_Init(void);
void MX_TIM6_Init(void);
void MX_I2C1_Init(void);
void MX_DCACHE1_Init(void);
void MX_ADC1_Init(void);
void MX_SPI1_Init(void);
void MX_USART3_UART_Init(void);

/* USER CODE BEGIN EFP */
// void SystemClock_Config(void);
/* USER CODE END EFP */

/* Private defines -----------------------------------------------------------*/
#define TP_INT_EXTI13_Pin GPIO_PIN_13
#define TP_INT_EXTI13_GPIO_Port GPIOC
#define TP_INT_EXTI13_EXTI_IRQn EXTI13_IRQn
#define KEY_MODE_Pin GPIO_PIN_14
#define KEY_MODE_GPIO_Port GPIOC
#define KEY_ENTER_Pin GPIO_PIN_15
#define KEY_ENTER_GPIO_Port GPIOC
#define KEY_FLASH_Pin GPIO_PIN_0
#define KEY_FLASH_GPIO_Port GPIOC
#define FLASH_TRI_Pin GPIO_PIN_1
#define FLASH_TRI_GPIO_Port GPIOC
#define FLASH_TRI_EXTI_IRQn EXTI1_IRQn
#define WKUP_EXTI0_Pin GPIO_PIN_0
#define WKUP_EXTI0_GPIO_Port GPIOA
#define WKUP_EXTI0_EXTI_IRQn EXTI0_IRQn
#define BL_NTF_Pin GPIO_PIN_2
#define BL_NTF_GPIO_Port GPIOA
#define RF_CSN_Pin GPIO_PIN_4
#define RF_CSN_GPIO_Port GPIOA
#define RF_SCK_Pin GPIO_PIN_5
#define RF_SCK_GPIO_Port GPIOA
#define RF_MISO_Pin GPIO_PIN_6
#define RF_MISO_GPIO_Port GPIOA
#define RF_MOSI_Pin GPIO_PIN_7
#define RF_MOSI_GPIO_Port GPIOA
#define CHG_OK_EXTI4_Pin GPIO_PIN_4
#define CHG_OK_EXTI4_GPIO_Port GPIOC
#define CHG_OK_EXTI4_EXTI_IRQn EXTI4_IRQn
#define LED_RED_Pin GPIO_PIN_5
#define LED_RED_GPIO_Port GPIOC
#define CAM_COMM_Pin GPIO_PIN_2
#define CAM_COMM_GPIO_Port GPIOB
#define TEST_1_Pin GPIO_PIN_11
#define TEST_1_GPIO_Port GPIOB
#define NIKON_SCS_Pin GPIO_PIN_12
#define NIKON_SCS_GPIO_Port GPIOB
#define NIKON_SCK_Pin GPIO_PIN_13
#define NIKON_SCK_GPIO_Port GPIOB
#define ENABLE_EXTI14_Pin GPIO_PIN_14
#define ENABLE_EXTI14_GPIO_Port GPIOB
#define ENABLE_EXTI14_EXTI_IRQn EXTI14_IRQn
#define NIKON_MOSI_Pin GPIO_PIN_15
#define NIKON_MOSI_GPIO_Port GPIOB
#define NIKON_MOSI_EXTI_IRQn EXTI15_IRQn
#define MAIN_SWITCH_Pin GPIO_PIN_11
#define MAIN_SWITCH_GPIO_Port GPIOD
#define MAIN_SWITCH_EXTI_IRQn EXTI11_IRQn
#define ENCODE_A_Pin GPIO_PIN_8
#define ENCODE_A_GPIO_Port GPIOA
#define ENCODE_A_EXTI_IRQn EXTI8_IRQn
#define ENCODE_B_Pin GPIO_PIN_9
#define ENCODE_B_GPIO_Port GPIOA
#define BOOST_EN_Pin GPIO_PIN_10
#define BOOST_EN_GPIO_Port GPIOA
#define BT_EN_Pin GPIO_PIN_15
#define BT_EN_GPIO_Port GPIOA
#define BT_RST_Pin GPIO_PIN_12
#define BT_RST_GPIO_Port GPIOC
#define RF_PA_RXEN_Pin GPIO_PIN_3
#define RF_PA_RXEN_GPIO_Port GPIOB
#define RF_PA_TXEN_Pin GPIO_PIN_4
#define RF_PA_TXEN_GPIO_Port GPIOB
#define RF_IRQ_Pin GPIO_PIN_5
#define RF_IRQ_GPIO_Port GPIOB
#define AMOLED_TE_Pin GPIO_PIN_6
#define AMOLED_TE_GPIO_Port GPIOB
#define AMOLED_TE_EXTI_IRQn EXTI6_IRQn
#define AMOLED_RST_Pin GPIO_PIN_7
#define AMOLED_RST_GPIO_Port GPIOB
#define TP_SCL_Pin GPIO_PIN_8
#define TP_SCL_GPIO_Port GPIOB
#define TP_SDA_Pin GPIO_PIN_9
#define TP_SDA_GPIO_Port GPIOB
#define TP_RST_Pin GPIO_PIN_0
#define TP_RST_GPIO_Port GPIOE

/* USER CODE BEGIN Private defines */

/* USER CODE END Private defines */

#ifdef __cplusplus
}
#endif

#endif /* __MAIN_H */
